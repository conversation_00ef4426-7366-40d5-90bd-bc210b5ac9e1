<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SearchGazettePeriod extends Model
{
    public static function getDisplayPeriod()
    {
        $currentYear = date('Y');
        $displayPeriod = [];
        for ($i = 0; $i < 4; $i++) {
            if ($i < 3) {
                $displayPeriod[] = $currentYear . '-' . ($currentYear - 4);
                $currentYear -= 5;
            } else {
                $displayPeriod[] = $currentYear . __('general.search_gazette.display_period_last');
            }
        }
        return $displayPeriod;
    }
}
