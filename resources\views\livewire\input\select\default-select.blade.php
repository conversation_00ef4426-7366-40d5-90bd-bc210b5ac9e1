@php

@endphp

<div id="{{ $id }}" class="default-select-dropdown-container">
    <div class="default-select-dropdown-dropdown-set">
        @if(isset($label) && !empty($label))
            <label class="label2 select-dropdown-label" for="dropdown-{{ $id }}">{{ $label }}</label>
        @endif

        <livewire:input.select.dropdown-button
                :$disabled
                :$type
                :$open
                :$id
                :$selected
                :$options
                :$firstOption
                :$customRender
                :$aria_label
        />
    </div>

    @if(!$disabled)
        <livewire:input.select.dropdown-menu
            :$name
            :$open
            :$id
            :$type
            :$selected
            :$options
            :$updateFn
        />
    @endif
</div>

@script
<script>
    $wire.$on('toggleCollapsed', () => {
        if ($wire.showHideContent) {
            const element = document.querySelector(`${$wire.showHideContent}`);
            if (element) {
                element.classList.toggle('option-open', $wire.open);
            }
        }
    });
</script>
@endscript
