/*---------*/
/* header */
/*---------*/
header {
    position: fixed;
    top: 0;
    width: 100%;
    background: linear-gradient(
          0deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.6) 35%,
          #fff 100%
    );
    backdrop-filter: blur(10px);
    z-index: 60;
    transition: 0.2s background ease-in-out;
}

header.active {
    background: var(--grey-white);
    transition: 0.2s background ease-in-out;
    box-shadow: var(--shadow-md);
}

header button {
    width: 28px;
    height: 28px;
    background: none;
    border: none;
}

header .gld-svg-container {
    color: var(--grey-white);
    min-width: 28px;
    min-height: 28px;
    width: 28px;
    height: 28px;
}

.gld-navbar {
    display: none;
}

.gld-header-container {
    padding-left: var(--spacing-big);
}

.gld-header-container .gld-header {
    height: var(--header-height-sm);
    max-height: var(--header-height-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.gld-header-container .gld-header .gld-header-logo {
    padding-right: var(--spacing-big);
}

.gld-header-container .gld-header .gld-img-container {
    max-width: 220px;
    max-height: 34px;
    width: 100%;
    height: 100%;
    display: flex;
}

.gld-header-container .gld-header .gld-img-container img {
    width: auto;
    height: 100%;
}

.gld-mobile-menu.open {
    right: 0;
    transition: right 0.3s ease;
}

.gld-mobile-menu {
    height: 52px;
    display: flex;
    position: fixed;
    top: 0;
    right: -260px;
    z-index: 9999;
    transition: right 0.3s ease;
    gap: var(--spacing-big);
}

.gld-mobile-menu .gld-menu {
    position: relative;
    background: linear-gradient(0deg, #2563EB, #3B82F6 80%);
    top: 0;
    height: 100vh;
    max-height: 100vh;
    max-width: 260px;
    width: calc(100vw - var(--spacing-xx-lg));
}

.gld-mobile-menu .gld-menu a {
    font-size: 1rem;
}


.gld-mobile-menu ul:not(:is(.mobile-lang-share-list, .share-items-list)) {
    width: 100%;
    height: 100%;
    overflow-x: auto;
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: start;
    gap: var(--spacing-xx-sm);
    padding: var(--header-height-sm) var(--spacing-container-sm);
}

.gld-mobile-menu ul:not(:is(.mobile-lang-share-list, .share-items-list)) li {
    display: block;
    width: 100%;
}

.gld-mobile-menu a {
    width: 100%;
    display: block;
    padding: var(--spacing-md) var(--spacing-x-sm);
    border-radius: var(--radius-sm);
    color: var(--grey-white);
    text-decoration: none;
}

.gld-mobile-menu a:hover {
    text-decoration: none;
    background: rgba(255, 255, 255, 0.10);
}

.gld-mobile-menu a.active {
    background: rgba(255, 255, 255, 0.10);
}


.gld-mobile-menu .mobile-lang-share-container {
    width: 100%;
    margin-top: var(--spacing-big);
}

.gld-mobile-menu .mobile-lang-share-container .mobile-lang-share-list {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-x-sm);
}

.gld-header-lang {
    width: max-content;
}

.gld-mobile-menu .mobile-lang-share-container .mobile-lang-share-list a {
    padding: var(--spacing-sm) var(--spacing-md);
    width: fit-content;
}

.gld-header-button-container {
    cursor: pointer;
    display: flex;
    height: var(--header-height-sm);
    width: fit-content;
    justify-content: center;
    align-items: center;
    padding-left: var(--spacing-big);
    border-radius: var(--radius-full) 0 0 var(--radius-full);
    background: linear-gradient(88deg, #2563EB -9.99%, #3B82F6 103.56%);
}

.gld-header-button-container::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 30px;
    height: 100%;
    width: 100vw;
    background: #3b82f6;
    z-index: -1;
}

.gld-desktop-menu {
    display: none;
}

.gld-desktop-menu ul {
    display: none;
}

.hamburger-icon {
    width: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
    padding: 2px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hamburger-icon .hamburger-icon-1, .hamburger-icon-2, .hamburger-icon-3 {
    width: 100%;
    height: 2px;
    border-radius: 3px;
    background-color: var(--grey-white);
    transition: all 0.3s ease;
}

.hamburger-icon .hamburger-icon-1 {
    transform: translateY(-4px);
}

.hamburger-icon .hamburger-icon-1.open {
    transform: rotate(40deg) translateX(3px);
}

.hamburger-icon .hamburger-icon-3 {
    transform: translateY(4px);
}

.hamburger-icon .hamburger-icon-3.open {
    transform: rotate(-40deg) translateX(3px);
}

.hamburger-icon .hamburger-icon-2.open {
    opacity: 0;
}

.search-icon::after {
    mask: url("/images/icon/search.svg") no-repeat;
    mask-size: cover;
}

.printer-icon::after {
    mask: url("/images/header/svg/print.svg") no-repeat;
    mask-size: cover;
}

.share-icon::after {
    mask: url("/images/header/svg/share_filled.svg") no-repeat;
    mask-size: cover;
}

.gld-header-share {
    position: relative;
}

.share-items-list {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
}

.share-items-list .share-item {
    padding: 0 !important;
    cursor: pointer;
    display: inline-block;
    min-width: 28px;
    height: 28px;
    border: none;
    background: none;
}

.share-items-container {
  position: absolute;
  pointer-events: none;
  left: -160px;
  top: -75px;
  opacity: 0;
  visibility: hidden;
  z-index: 5;
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-sm);
  background: var(--grey-white);
  transition: top 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
}

.share-items-container::before {
    content: "";
    display: block;
    position: absolute;
    bottom: -10px;
    right: 30px;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 15px solid var(--grey-white);
}

.share-items-container.open {
    pointer-events: auto;
    top: -70px;
    visibility: visible;
    opacity: 1;
    transition: top 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
}

.gld-header-container .overlay {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.gld-header-container .overlay.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

@media (min-width: 992px) {
    .gld-mobile-menu {
        display: none;
    }

    .gld-desktop-menu {
        display: flex;
        align-items: center;
    }

    .gld-header-container {
        display: flex;
        flex-direction: column;
        max-width: 1440px;
        margin: auto;
        padding: 0 var(--spacing-container);
    }

    .gld-header-container .overlay {
        display: none;
    }

    .gld-header-container .gld-header {
        height: var(--header-height);
        max-height: var(--header-height);
    }

    .gld-header-container .gld-img-container {
        min-width: 332px;
        max-width: 332px;
        min-height: 52px;
        max-height: 52px;
        width: 332px;
        height: 52px;
    }

    .gld-header-container .gld-header .gld-header-logo {
        height: 51px;
        padding: var(--spacing-x-sm) 0;
        padding-right: var(--spacing-x-big);
    }

    .gld-navbar {
        display: block;
    }

    .gld-navbar ul {
        display: flex;
        /*padding: 0 var(--spacing-container);*/
        padding: 0 var(--spacing-big);
        justify-content: center;
        gap: var(--spacing-big);
        /*gap: var(--spacing-xxx-big);*/
    }

    .gld-navbar a {
        display: inline-block;
        padding: var(--spacing-sm) 0;
        text-decoration: none;
        color: var(--grey-900);
        position: relative;
    }

    .gld-navbar a:hover {
        text-decoration: none;
    }

    .gld-navbar a.active {
        color: var(--primary-600);
    }

    .gld-navbar a::before {
        content: "";
        position: absolute;
        width: 100%;
        height: 3px;
        border-radius: 4px;
        background-color: var(--primary-600);
        bottom: 6px;
        left: 0;
        transform-origin: right;
        transform: scaleX(0);
        transition: transform 0.3s ease-in-out;
    }

    .gld-navbar a:hover::before {
        transform-origin: left;
        transform: scaleX(1);
    }

    .gld-header-button-container {
        cursor: default;
        position: relative;
        height: 100%;
        background: linear-gradient(
            88deg,
            var(--primary-600) -9.99%,
            #3b82f6 103.56%
        );
        padding-left: var(--spacing-xx-lg);
        border-radius: var(--radius-full);
    }

    .gld-header-button-container ul {
        display: flex;
        gap: var(--spacing-big);
        align-items: center;
    }

    .gld-header-button-container ul a {
        color: var(--grey-white);
        text-decoration: none;
    }

    .share-items-list {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .share-items-list li:hover {
        transform: scale(1.05);
        transition: transform 0.2s ease-in-out;
    }

    .share-items-list .share-item {
        width: 48px;
        height: 48px
    }

    .share-items-container {
        left: -310px;
        top: 45px;
    }

    .share-items-container::before {
        top: -13px;
        right: 10px;
        transform: rotate(180deg);
    }

    .share-items-container.open {
        pointer-events: auto;
        top: 50px;
        visibility: visible;
        opacity: 1;
        transition: top 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
    }
}

@media (min-width: 1080px) {
    .gld-navbar ul {
        padding: 0 var(--spacing-container);
        gap: var(--spacing-xxx-big);
    }
}

.gld-header-search {
    position: relative;
}

.gld-header-search .search-icon {
    width: 28px;
    height: 28px;
}

@media (max-width: 768px) {
    .gld-header-search a {
        width: 28px !important;
        height: 30px !important;
        padding: 0px !important;
    }
}