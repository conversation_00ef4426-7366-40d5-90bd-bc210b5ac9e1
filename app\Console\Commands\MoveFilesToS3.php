<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use DirectoryIterator;

class MoveFilesToS3 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:move-files-to-s3';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Put all files from the local storage to the S3 bucket (temp use)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        set_time_limit(0);
        $baseFolderPath = storage_path('app/private/tobeuploaded');
        $baseDirectory = new DirectoryIterator($baseFolderPath);

        foreach ($baseDirectory as $folder) {
            if ($folder->isDot()) {
                continue;
            }

            $folderName = $folder->getFilename();
            echo "Processing folder: " . $folderName . "\n";

            if (Storage::disk('object_storage')->exists($folderName)) {
                Storage::disk('object_storage')->makeDirectory($folderName);
            }

            $folderPath = $baseFolderPath . '/' . $folderName;

            if ($folder->isDir()) {
                $subDirectory = new DirectoryIterator($folderPath);
                foreach ($subDirectory as $file) {
                    if ($file->isDot()) {
                        continue;
                    }

                    if ($file->isDir()) {
                        // Skip directories
                        $subFolderName = $file->getFilename();
                        echo "Processing sub-folder: " . $subFolderName . "\n";
                        $subFolderPath = $baseFolderPath . '/' . $folderName . '/' . $subFolderName;

                        if (Storage::disk('object_storage')->exists($folderName . '/' . $subFolderName)) {
                            Storage::disk('object_storage')->makeDirectory($folderName . '/' . $subFolderName);
                        }

                        $subSubDirectory = new DirectoryIterator($subFolderPath);
                        foreach ($subSubDirectory as $subFile) {
                            if ($subFile->isDot()) {
                                continue;
                            }

                            $subFileName = $subFile->getFilename();
                            $subFilePath = $subFolderPath . '/' . $subFileName;

                            // Process the file here
                            echo "Processing file: " . $subFilePath . "\n";

                            Storage::disk('object_storage')->putFileAs(
                                $folderName . '/' . $subFolderName,
                                new \Illuminate\Http\File($subFilePath),
                                $subFileName
                            );
                        }
                    } else {
                        $fileName = $file->getFilename();
                        $filePath = $folderPath . '/' . $fileName;

                        // Process the file here
                        echo "Processing file: " . $filePath . "\n";

                        Storage::disk('object_storage')->putFileAs(
                            $folderName,
                            new \Illuminate\Http\File($filePath),
                            $fileName
                        );
                    }
                }
            }
        }
    }
}
