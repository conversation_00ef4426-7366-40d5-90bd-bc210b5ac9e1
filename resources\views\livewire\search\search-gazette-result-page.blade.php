<div class="gld-main-wrapper pages-body" id="search-gazette-result-body">
    <div class="loading-screen" wire:loading.delay.long>
        <div class="loading-content">
            <span class="loader"></span>
            <span class="body2 loading-text">{{ __('general.loading') }}</span>
        </div>
    </div>
    <div class="gld-main-container">
        <section id="search-gazette-bar">
            <livewire:search.search-gazette-bar />
        </section>
        <section id="search-gazette-filter">
            <livewire:search.search-gazette-filter
                    :$period
                    :$searchKeyword
                    :$yearAndVolume
                    :$yearAndVolumeOptions
                    :$dateAndIssue
                    :$dateAndIssueOptions
                    :$supplementNo
                    :$group
                    :$categoriesResultCount
                    wire:model.live="category"
            />
        </section>
        @if (!empty($searchKeyword))
            <section id="search-gazette-result-table">
                <div class="search-gazette-result-table-filter">
                    <div class="table-column-filter">
                        <livewire:input.select.default-select
                            name="column"
                            updateFn="update-property"
                            id="table-column-filter"
                            customRender="{{ __('general.table.columns_visibility') }}"
                            :type="['button' => 'text', 'options' => 'checkbox']"
                            :selected="$column"
                            :disabled="false"
                            :options="[
                                '1' => __('general.table.columns_visibility_1'),
                                '2' => __('general.table.columns_visibility_2'),
                                '3' => __('general.table.columns_visibility_3'),
                                '4' => __('general.table.columns_visibility_4'),
                                '5' => __('general.table.columns_visibility_5'),
                                '6' => __('general.table.columns_visibility_6')
                            ]"
                        />
                    </div>
                    <div class="table-order-by-filter">
                        <livewire:input.select.default-select
                            name="orderBy"
                            updateFn="update-property"
                            id="table-orderBy-filter"
                            label="{{ __('general.table.order_by') }}"
                            :type="['button' => 'text', 'options' => 'default']"
                            :selected="$orderBy"
                            :disabled="false"
                            :options="[
                                '1' => __('general.table.relevance'),
                                '2' => __('general.table.order_publish_newest'),
                                '3' => __('general.table.order_publish_oldest'),
                                '4' => __('general.table.order_publish_asc'),
                                '5' => __('general.table.order_publish_desc'),
                            ]"
                        />
                    </div>
                </div>
                <livewire:table.search-table
                    :pagination="false"
                    :notices="$this->notices"
                    :paginationData="$this->paginationData"
                    :$page
                    :column="$this->column"
                    :isLs6="$this->isLs6"

                />
                <div class="table-pagination">
                    <div class="left">
                        <livewire:input.select.default-select
                            name="perPage"
                            id="select-table-row-options"
                            aria_label="{{__('general.pagination.select_row_per_page')}}"
                            :type="['button' => 'white', 'options' => 'default']"
                            :selected="$paginationData['per_page']"
                            :disabled="false"
                            :options="[
                        '5' => '5',
                        '20' => '20',
                        '50' => '50']"
                            updateFn="update-property"
                        />
                        <div class="label2 table-per-page">
                            {{__('general.pagination.per_page', [
                                'current' => ($paginationData['current_page'] - 1) * $paginationData['per_page'] + 1,
                                'current_total' => min($paginationData['current_page'] * $paginationData['per_page'], $paginationData['total']),
                                'total' => $paginationData['total']
                            ])}}
                            {{--            Show {{ ($paginationData['current_page'] - 1) * $paginationData['per_page'] + 1 }}-{{ min($paginationData['current_page'] * $paginationData['per_page'], $paginationData['total']) }} of {{ $paginationData['total'] }}--}}
                        </div>
                    </div>
                    <div class="right">
                        <button class="table-prev-btn" wire:click="setPreviousPage" title="{{__('general.pagination.previous')}}" aria-label="{{__('general.pagination.previous')}}" @if($paginationData['current_page'] <= 1 || $prevDisabled) disabled @endif>
                            <span class="gld-svg-container">
                                <span title="Previous page" class="prev-btn-icon svg"></span>
                            </span>
                        </button>
                        <div class="table-select-page label2">
                            {{__('general.pagination.page')}}
                            <input type="text" id="currentPage" name="currentPage" value="{{ $currentPage }}" aria-label="{{__('general.pagination.go_to_page')}}" class="number-input label2" wire:model.lazy="currentPage" oninput="this.value = this.value.replace(/[^0-9]/g, '');" />
                            {{__('general.pagination.of')}} {{ $paginationData['last_page'] }}
                        </div>
                        <button class="table-next-btn" wire:click="setNextPage" title="{{__('general.pagination.next')}}" aria-label="{{__('general.pagination.next')}}" @if($paginationData['current_page'] >= $paginationData['last_page'] || $nextDisabled) disabled @endif>
                            <span class="gld-svg-container">
                                <span title="Next page" class="next-btn-icon svg"></span>
                            </span>
                        </button>
                    </div>
                </div>
            </section>
        @endif
    </div>
</div>
@script
<script>
    $wire.$watch('paginationData.per_page', (value) => {
        // Scroll to the top of the table
        const table = document.getElementById('search-results-table');
        if (table) {
            table.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    });
</script>
@endscript

