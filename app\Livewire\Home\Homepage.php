<?php

namespace App\Livewire\Home;

use App\Models\WebsiteContent;
use Livewire\Component;
use Illuminate\Support\Facades\Cookie;

class Homepage extends Component
{
    public array $data = [];

    public string $setSearchKeyword = '';
    public array $keys = [
        'general_email',
        'general_fax',
        'general_address',
        'general_gazette_tel',
    ];
    public array $othersKeys = [
        'announcement_title',
        'announcement_subtitle',
        'announcement_content',
        'announcement_subtitle2',
        'announcement_card1_title',
        'announcement_card1_name',
        'announcement_card1_address',
        'announcement_card1_tel',
        'announcement_card1_email',
        'announcement_card2_title',
        'announcement_card2_name',
        'announcement_card2_address',
        'announcement_card2_tel',
        'announcement_card2_email',
    ];

    public function redirectToSearchResult()
    {
        $cookieConsent = Cookie::get('cookie_consent');
        if ($cookieConsent === '1' && !empty(trim($this->setSearchKeyword))) {
            $keywords = json_decode(Cookie::get('search_keywords', '[]'), true);

            if (($key = array_search($this->setSearchKeyword, $keywords)) !== false) {
                unset($keywords[$key]);
            }

            array_unshift($keywords, $this->setSearchKeyword);

            if (count($keywords) > 10) {
                array_pop($keywords);
            }

            Cookie::queue('search_keywords', json_encode($keywords), 60 * 24 * 30);
            $this->keywords = $keywords;
        }
            return redirect()->route('search-gazette-result', ['kw' => $this->setSearchKeyword, 'locale' => app()->getLocale()]);
    }

    public function render()
    {
        $locale = app()->getLocale();
        $content = $locale . '_content';

        $contents = WebsiteContent::where('page_name', 'Contact')
            ->whereIn('key', $this->keys)
            ->get()
            ->pluck($content, 'key');

        $classes = [
            '<p>' => '',
            '</p>' => '',
        ];

        foreach ($this->keys as $key) {
            $this->data[$key] = addClasses($contents[$key], $classes);
        }

        $announcement_contents = WebsiteContent::where('page_name', 'Others')
            ->whereIn('key', $this->othersKeys)
            ->get()
            ->pluck($content, 'key');
        
        foreach ($this->othersKeys as $othersKey) {
            $this->data[$othersKey] = html_entity_decode($announcement_contents[$othersKey]  ?? '');
        }

        return view('livewire.home.homepage');
    }
}
