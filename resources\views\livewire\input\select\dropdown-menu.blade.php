<div class="select-dropdown-menu {{ $open ? 'show' : null }}" aria-labelledby="dropdown-{{ $id }}" role="listbox" >
    <ul @if($open && $type['options'] === 'checkbox') wire:click.outside="$parent.closeMenu" @endif>
        @switch($type['options'])
            @case('default')
                @foreach($options as $key => $option)
                    <li
                        tabindex="0"
                        class="label2 default-select-dropdown-items {{ (string)$selected === (string)$key ? 'selected' : null }}"
                        wire:click="$parent.selectOption('{{ $key }}')"
                        role="option"
                        aria-selected="{{ (string)$selected === (string)$key ? 'true' : 'false' }}"
                        wire:keydown.enter="$parent.selectOption('{{ $key }}')"
                        wire:keydown.space="$parent.selectOption('{{ $key }}')"
                    >
                        {{ $option }}
                    </li>
                @endforeach
                @break
            @case('checkbox')
                @foreach($options as $key => $option)
                    <li
                        tabindex="0"
                        class="label2 checkbox-select-dropdown-items {{ in_array($key, $selected) ? 'selected' : null }}"
                        role="option"
                        aria-selected="{{ in_array($key, $selected) ? 'true' : 'false' }}"
                    >
                        <input type="checkbox" name="{{ $name }}" id="{{ $name . '-'. $key }}" value="{{ $key }}" wire:model.live="selectedArray" />
                        <label for="{{ $name . '-'. $key }}">
                            <span class="checkmark"></span>
                            {{ $option }}
                        </label>
                    </li>
                @endforeach
                @break
        @endswitch
    </ul>
</div>
@script
<script>
    $wire.$watch('selected', () => {
        $wire.$call('updateArray');
    });
</script>
@endscript
