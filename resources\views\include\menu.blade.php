<nav class="gld-menu" aria-label="Main Navigation">
    <ul>
        @foreach([
            'home' => 'Home',
            'search-gazette' => 'Search Gazette Notice',
            'list-of-gazette' => 'List of Gazette',
            'whats-new' => 'What’s New',
            'contact-us' => 'Contact Us'
        ] as $route => $label)
            <li>
                <a href="{{ route($route, ['locale' => $locale]) }}" wire:ignore class="label1 {{ request()->routeIs($route.'*') ? 'active' : '' }}" >{{ __('general.menu.' . $label) }}</a>
            </li>
        @endforeach
        @if($mobile)
            <li><a href="{{ route('text-size', ['locale' => $locale]) }}" class="label1 {{ $activeRoute === 'text-size' ? 'active' : '' }}" >{{ __('general.header.text_size') }}</a></li>
            <div class="mobile-lang-share-container">
                <ul class="mobile-lang-share-list">
                    <li class="gld-header-lang">
                        <a href="{{ route('change.lang', $locale === 'en' ? 'zh' : 'en') }}" class="label2">
                            {{ __('general.header.lang') }}
                        </a>
                    </li>
                    <li class="gld-header-search">
                        <a href="{{ route('search-gazette', app()->getLocale()) }}" type="submit" title="{{ __('general.home.banner_search_button') }}">
                            <span class="search-icon svg"></span>
                        </a>
                    </li>
                    <li class="gld-header-print">
                        <button title="{{ __('general.header.print') }}" wire:click.prevent="printPage" type="button">
                            <span class="sr-only">{{ __('general.header.print') }}</span>
                            <span class="gld-svg-container">
                                <span class="printer-icon svg"></span>
                            </span>
                        </button>
                    </li>
                    <li class="gld-header-share" @if($showShare) wire:click.outside="$set('showShare', false)" @endif wire:click.prevent="toggleShare">
                        <button title="{{ __('general.header.share') }}" type="button">
                            <span class="sr-only">{{ __('general.header.share') }}</span>
                            <span class="gld-svg-container">
                                 <img src="{{ asset('images/header/svg/share_filled.svg') }}" alt="{{ __('general.header.share') }}">
                            </span>
                        </button>
                        <div class="share-items-container {{ $showShare ? 'open' : '' }}">
                            <ul class="share-items-list">
                                <li class="share-email-icon">
                                    <a wire:click="shareByEmail" class="share-item">
                                        <img src="{{ asset('images/header/svg/Email.svg') }}" alt="{{ __('general.header.share_email') }}" title="{{ __('general.header.share_email') }}"/>
                                    </a>
                                </li>
                                <li class="share-facebook-icon">
                                    <button wire:click="openSharePopup('https://www.facebook.com/sharer/sharer.php?u={{ $shareContent['url'] }}')" class="share-item" type="button">
                                        <img src="{{ asset('images/header/svg/Facebook.svg') }}" alt="{{ __('general.header.share_fb') }}" title="{{ __('general.header.share_fb') }}"/>
                                    </button>
                                </li>
                                <li class="share-whatsapp-icon">
                                    <button wire:click="openSharePopup('https://api.whatsapp.com/send?text={{ $shareContent['url'] }}')" class="share-item" type="button">
                                        <img src="{{ asset('images/header/svg/WhatsApp.svg') }}" alt="{{ __('general.header.share_whatsapp') }}" title="{{ __('general.header.share_whatsapp') }}"/>
                                    </button>
                                </li>
                                <li class="share-weibo-icon">
                                    <button wire:click="openSharePopup('https://service.weibo.com/share/share.php?url={{ $shareContent['url'] }}&title={{ $shareContent['title'] }}')" class="share-item" type="button">
                                        <img src="{{ asset('images/header/svg/Weibo.svg') }}" alt="{{ __('general.header.share_weibo') }}" title="{{ __('general.header.share_weibo') }}"/>
                                    </button>
                                </li>
                                <li class="share-x-icon">
                                    <button wire:click="openSharePopup('https://x.com/intent/tweet?text={{ $shareContent['body'] }}')" class="share-item" type="button">
                                        <img src="{{ asset('images/header/svg/X.svg') }}" alt="{{ __('general.header.share_x') }}" title="{{ __('general.header.share_x') }}"/>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        @endif
    </ul>
</nav>
