<?php
$locale = app()->getLocale();
?>
<section class="gld-main-wrapper pages-body" id="list-of-gazette-body">
    <div class="gld-main-container">
        <section class="hksar-section" id="the-gov-hksar">
            <div class="section-header toggle-btn" wire:click="$toggle('isShow')">
                <h2 class="gld-highlight-title h2">{{ __('general.important_notices.the_government_of_HKSAR_gazette') }}</h2>
                <button type="button" class="label1 expand-btn" title="{{ $isShow ? __('general.collapse') : __('general.expand') }}">
                    <span class="sr-only">{{ $isShow ? __('general.collapse') : __('general.expand') }}</span>
                    <span class="gld-svg-container {{ $isShow ? 'expand' : 'collapse' }}">
                            <span class="arrow-icon svg"></span>
                    </span>
                </button>
            </div>
            <div class="expand-container {{ $isShow ? 'open' : 'hide' }}">
                <div class="gld-card">
                    <livewire:content.hksar-gazatte />
                </div>
            </div>
        </section>
        <section class="list-of-gazette-table-section">
            <h2 class="gld-highlight-title h2">{{ __('general.list_of_gazette.list_of_gazette') }}</h2>
            <div class="gld-search-table-wrapper">
                <div class="gld-default-table-container gld-search-table-container">
                    <div class="gld-search-table">
                        @foreach($gazettes as $gazette)
                        <div class="gld-search-table-col">
                            <div class="gld-search-table-row">
                                <div class="left label2">{{ __('general.list_of_gazette.gazette_date') }}</div>
                                <div class="right label1">
                                    @if ($locale == 'en')
                                        {{ $gazette->enDayOfWeek }}<br/>{{ __('general.search_gazette.date_format_table', ['day' => $gazette->dayDate, 'month' => $gazette->monthDate, 'year' => $gazette->yearDate]) }}<br/>{{ __('general.search_gazette.date_issue', ['issue_num' => $gazette->gno]) }} {{ __('general.search_gazette.year_volume', ['volume' => $gazette->volume]) }}
                                    @else
                                        {{ __('general.search_gazette.date_format_table', ['day' => $gazette->dayDate, 'month' => $gazette->monthDate, 'year' => $gazette->yearDate]) }} {{ $gazette->zhDayOfWeek }}<br/>{{ __('general.search_gazette.year_volume', ['volume' => $gazette->volume]) }} {{ __('general.search_gazette.date_issue', ['issue_num' => $gazette->gno]) }}
                                    @endif
                                    @if ($gazette->extra === 1)
                                        <br/>{{ __('general.search_gazette.extraordinary') }}
                                    @endif
                                </div>
                            </div>
                            <div class="gld-search-table-row">
                                <div class="left label2">{{ __('general.list_of_gazette.published_gazette') }}</div>
                                <div class="right label1">
                                    <ul class="published-gazette-list">
                                        @if (count($gazette->mg) > 0)
                                            <li class="published-gazette-link">
                                                <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'mg', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_gn') }}</a>
                                            </li>
                                        @endif

                                        @if (count($gazette->ls1) > 0)
                                            <li class="published-gazette-link">
                                                <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls1', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn1') }}</a>
                                            </li>
                                        @endif

                                        @if (count($gazette->ls2) > 0)
                                            <li class="published-gazette-link">
                                                <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls2', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn2') }}</a>
                                            </li>
                                        @endif

                                        @if (count($gazette->ls3) > 0)
                                            <li class="published-gazette-link">
                                                <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls3', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn3') }}</a>
                                            </li>
                                        @endif

                                        @if (count($gazette->ls4) > 0)
                                            <li class="published-gazette-link">
                                                <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls4', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn4') }}</a>
                                            </li>
                                        @endif

                                        @if (count($gazette->ls5) > 0)
                                            <li class="published-gazette-link">
                                                <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls5', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn5') }}</a>
                                            </li>
                                        @endif

                                        @if (count($gazette->ls6) > 0)
                                            <li class="published-gazette-link">
                                                <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls6', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn6') }}</a>
                                            </li>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                    <div class="gld-search-table-container">
                        <table class="gld-default-table">
                            <thead>
                            <tr>
                                <th class="gld-default-table-header label2" style="min-width: 200px">{{ __('general.list_of_gazette.gazette_date') }}</th>
                                <th class="gld-default-table-header label2" style="min-width: 300px; width: 100%">{{ __('general.list_of_gazette.published_gazette') }}</th>
                            </tr>
                            </thead>
                            <tbody>
                                @foreach($gazettes as $gazette)
                                    <tr>
                                        <td class="gld-default-table-data label1">
                                            @if ($locale == 'en')
                                                {{ $gazette->enDayOfWeek }}<br/>{{ __('general.search_gazette.date_format_table', ['day' => $gazette->dayDate, 'month' => $gazette->monthDate, 'year' => $gazette->yearDate]) }}<br/>{{ __('general.search_gazette.date_issue', ['issue_num' => $gazette->gno]) }} {{ __('general.search_gazette.year_volume', ['volume' => $gazette->volume]) }}
                                            @else
                                                {{ __('general.search_gazette.date_format_table', ['day' => $gazette->dayDate, 'month' => $gazette->monthDate, 'year' => $gazette->yearDate]) }} {{ $gazette->zhDayOfWeek }}<br/>{{ __('general.search_gazette.year_volume', ['volume' => $gazette->volume]) }} {{ __('general.search_gazette.date_issue', ['issue_num' => $gazette->gno]) }}
                                            @endif
                                            @if ($gazette->extra === 1)
                                                <br/>{{ __('general.search_gazette.extraordinary') }}
                                            @endif
                                        </td>
                                        <td class="gld-default-table-data label1">
                                            <ul class="published-gazette-list">
                                                @if (count($gazette->mg) > 0)
                                                    <li class="published-gazette-link">
                                                        <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'mg', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_gn') }}</a>
                                                    </li>
                                                @endif

                                                @if (count($gazette->ls1) > 0)
                                                    <li class="published-gazette-link">
                                                        <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls1', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn1') }}</a>
                                                    </li>
                                                @endif

                                                @if (count($gazette->ls2) > 0)
                                                    <li class="published-gazette-link">
                                                        <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls2', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn2') }}</a>
                                                    </li>
                                                @endif

                                                @if (count($gazette->ls3) > 0)
                                                    <li class="published-gazette-link">
                                                        <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls3', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn3') }}</a>
                                                    </li>
                                                @endif

                                                @if (count($gazette->ls4) > 0)
                                                    <li class="published-gazette-link">
                                                        <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls4', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn4') }}</a>
                                                    </li>
                                                @endif

                                                @if (count($gazette->ls5) > 0)
                                                    <li class="published-gazette-link">
                                                        <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls5', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn5') }}</a>
                                                    </li>
                                                @endif

                                                @if (count($gazette->ls6) > 0)
                                                    <li class="published-gazette-link">
                                                        <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls6', 'locale' => app()->getLocale()]) }}">{{ __('general.search_gazette.category_sn6') }}</a>
                                                    </li>
                                                @endif
                                            </ul>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <livewire:table.pagination
                        :$paginationData
                        :$page
                    />
            </div>
        </section>
    </div>
</section>
@script
<script>
    $wire.$watch('isShow', () => {
        const expandContainer = document.querySelector('.expand-container');
        document.querySelector('.expand-container').style.maxHeight = document.querySelector('.expand-container').scrollHeight + 'px';
        expandContainer.style.maxHeight = expandContainer.scrollHeight + 'px';
        if ($wire.$get('isShow')) {
            expandContainer.style.maxHeight = expandContainer.scrollHeight + 'px';
        } else {
            expandContainer.style.maxHeight = '0';
        }
    });
</script>
@endscript
