<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Gazette extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'gazette';
    protected $primaryKey = 'id';

    const MINYEAR = 1996;
    const PUBLISHEDMINYEAR = 2000;

    public function mg()
    {
        return $this->hasMany(Mg::class, 'gazette_id', 'id');
    }

    public function ls1()
    {
        return $this->hasMany(Ls1::class, 'gazette_id', 'id');
    }

    public function ls2()
    {
        return $this->hasMany(Ls2::class, 'gazette_id', 'id');
    }

    public function ls3()
    {
        return $this->hasMany(Ls3::class, 'gazette_id', 'id');
    }

    public function ls4($parent = null)
    {
        $ls4 = [];
        if ($this->extra == 0) {
            $ls4 = Ls4::where('dateno', $this->dateno)->where('volume', $this->volume)->where('gno', $this->gno)->where('p_level_id', $parent)->get();
        }
        return $ls4;
    }

    public function ls5()
    {
        return $this->hasMany(Ls5::class, 'gazette_id', 'id');
    }

    public function ls6()
    {
        $ls6 = [];
        if ($this->extra == 0) {
            $ls6 = Ls6::where('dateno', $this->dateno)->where('volume', $this->volume)->where('gno', $this->gno)->get();
        }

        return $ls6;
    }
}
