<header class="{{ $headerActive ? 'active' : '' }}">
    <div class="gld-header-container">
        <div class="overlay @if($open) show @endif" wire:click.stop="$toggle('open')"></div>
        <div class="gld-header">
            <!--        logo         -->
            <div class="gld-header-logo">
                <div class="gld-img-container">
                    <a href="{{ route('home', ['locale' => $locale]) }}" >
                        <img src="{{ app()->getLocale() === 'zh' ? asset('images/header/svg/HKSAR-Logo.svg') : asset('images/header/svg/HKSAR-Logo.svg') }}" alt="{{ __('general.web_name') }}" title="{{ __('general.web_name') }}"/>
                    </a>
                </div>
            </div>
            <!--        header buttons         -->
            <div class="gld-desktop-menu gld-header-button-container">
                <ul>
                    <li class="gld-header-fs">
                        <a href="{{ route('text-size', ['locale' => $locale]) }}" class="label2" >{{ __('general.header.text_size')}}</a>
                    </li>
                    <li class="gld-header-lang">
                        <a href="{{ route('change.lang', $locale === 'en' ? 'zh' : 'en') }}" class="label2">
                            {{ __('general.header.lang') }}
                        </a>
                    </li>
                    <li class="gld-header-search">
                        <a href="{{ route('search-gazette', app()->getLocale()) }}" type="submit" title="{{ __('general.home.banner_search_button') }}">
                            <span class="search-icon svg"></span>
                        </a>
                    </li>
                    <li class="gld-header-print">
                        <button title="{{ __('general.header.print') }}" wire:click.prevent="printPage" type="button">
                            <span class="sr-only">{{ __('general.header.print') }}</span>
                            <span class="gld-svg-container">
                                <span class="printer-icon svg"></span>
                            </span>
                        </button>
                    </li>
                    <li class="gld-header-share" @if($showShare) wire:click.outside="$set('showShare', false)" @endif wire:click="$toggle('showShare')">
                        <button title="{{ __('general.header.share') }}" type="button">
                            <span class="sr-only">{{ __('general.header.share') }}</span>
                            <span class="gld-svg-container">
                                <span class="share-icon svg"></span>
                            </span>
                        </button>
                        <div class="share-items-container {{ $showShare ? 'open' : '' }}">
                            <ul class="share-items-list">
                                <li class="share-email-icon">
                                    <a wire:click="shareByEmail" class="share-item" href="javascript:;">
{{--                                    <a href="mailto:?subject={{ $shareContent['title'] }}&body={{ $shareContent['body'] }}" class="share-item">--}}
                                        <img src="{{ asset('images/header/svg/Email.svg') }}" alt="{{ __('general.header.share_email') }}" title="{{ __('general.header.share_email') }}"/>
                                    </a>
                                </li>
                                <li class="share-facebook-icon">
                                    <button wire:click="openSharePopup('https://www.facebook.com/sharer/sharer.php?u={{ $shareContent['url'] }}')" class="share-item" type="button">
                                        <img src="{{ asset('images/header/svg/Facebook.svg') }}" alt="{{ __('general.header.share_fb') }}" title="{{ __('general.header.share_fb') }}"/>
                                    </button>
                                </li>
                                <li class="share-whatsapp-icon">
                                    <button wire:click="openSharePopup('https://api.whatsapp.com/send?text={{ $shareContent['url'] }}')" class="share-item" type="button">
                                        <img src="{{ asset('images/header/svg/WhatsApp.svg') }}" alt="{{ __('general.header.share_whatsapp') }}" title="{{ __('general.header.share_whatsapp') }}"/>
                                    </button>
                                </li>
                                <li class="share-weibo-icon">
                                    <button wire:click="openSharePopup('https://service.weibo.com/share/share.php?url={{ $shareContent['url'] }}&title={{ $shareContent['title'] }}')" class="share-item" type="button">
                                        <img src="{{ asset('images/header/svg/Weibo.svg') }}" alt="{{ __('general.header.share_weibo') }}" title="{{ __('general.header.share_weibo') }}"/>
                                    </button>
                                </li>
                                <li class="share-x-icon">
                                    <button wire:click="openSharePopup('https://x.com/intent/tweet?text={{ $shareContent['body'] }}')" class="share-item" type="button">
                                        <img src="{{ asset('images/header/svg/X.svg') }}" alt="{{ __('general.header.share_x') }}" title="{{ __('general.header.share_x') }}"/>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
            <!--        mobile menu         -->
            <div class="gld-mobile-menu {{ $open ? 'open' : '' }}">
                <div class="gld-header-button-container" wire:click="$toggle('open')">
                    <div class="hamburger-icon" id="icon" title="{{ $open ? __('general.header.close_menu') : __('general.header.open_menu') }}">
                        <div class="hamburger-icon-1 {{ $open ? 'open' : '' }}"></div>
                        <div class="hamburger-icon-2 {{ $open ? 'open' : '' }}"></div>
                        <div class="hamburger-icon-3 {{ $open ? 'open' : '' }}"></div>
                    </div>
                </div>
                @include('include.menu', [
                    'activeRoute' => $activeRoute,
                    'mobile' => true,
                ])
            </div>
            <!--        desktop menu         -->
        </div>
        <div class="gld-navbar">
            @include('include.menu', ['activeRoute' => $activeRoute, 'mobile' => false])
        </div>
    </div>

    @script
        <script>
            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    if (!$wire.headerActive) {
                        $wire.$el.classList.add('active');
                        $wire.$call('toggleHeaderActive', true);
                    }
                } else {
                    if ($wire.headerActive) {
                        $wire.$el.classList.remove('active');
                        $wire.$call('toggleHeaderActive', false);
                    }
                }
            });

            $wire.$on('printPage', () => {
                window.print();
            });

            $wire.$on('openSharePopup', (url) => {
                const width = 600;
                const height = 400;
                const left = (window.innerWidth / 2) - (width / 2);
                const top = (window.innerHeight / 2) - (height / 2);

                setTimeout(() => {
                    window.open(
                        url,
                        'Share',
                        `width=${width},height=${height},top=${top},left=${left},toolbar=no,menubar=no,scrollbars=yes,resizable=yes`
                    );
                }, 0);
            });

            $wire.$on('openMailto', (mailto) => {
                window.location.href = mailto;
            });
        </script>
    @endscript
</header>
