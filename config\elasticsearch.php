<?php

return [
    'ES_HOSTS' => env('ES_HOSTS', 'http://localhost:9200'),
    'ES_USERNAME' => env('ES_USERNAME', 'elastic'),
    'ES_PASSWORD' => env('ES_PASSWORD', 'changeme'),
    'ES_INDEX' => env('ES_INDEX', 'anonymous_sessions'),
    'ES_SNAPSHOT_REPO' => env('ES_SNAPSHOT_REPO', 'test'),
    'ES_BULK_INDEX_SIZE' => env('ES_BULK_INDEX_SIZE', 30),
    'ES_RETRY_TIME=' => env('ES_RETRY_TIME', 2),
    'ES_CHUNK_SIZE' => env('ES_CHUNK_SIZE', 1),
    'ES_SIZE' => env('ES_SIZE', 600),
    'ES_RESULT_SIZE' => env('ES_RESULT_SIZE', 50),
    'ES_RECENT_YEARS' => env('ES_RECENT_YEARS', 100),
];