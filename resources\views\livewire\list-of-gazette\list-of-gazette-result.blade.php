<section class="gld-main-wrapper pages-body" id="list-of-gazette-result-body">
    @if ($this->tableName == 'ls4')
        <div class="gld-main-container">
            <livewire:list-of-gazette.list-of-gazette-result-table-ls4
                :title="$this->tableName"
                :notices="$this->notices"
                :gazette="$this->gazette"
            />
        </div>
    @else
        @foreach($this->notices as $noticeType => $noticeGroup)
          @php
            $locale = app()->getLocale();

            $id = $noticeGroup[0]->type_detail['id'] ?? null;

            if ($locale == 'en') {
                $noticeType = $noticeGroup[0]->type_detail['e_type'] ?? '';
            } else {
                $noticeType = $noticeGroup[0]->type_detail['c_type'] ?? '';
            }

            if (preg_match('/^<!--.*-->$/', $noticeType)) {
                $noticeType = '';
            }

              $isLs6 = false;
              if ($this->tableName == 'ls6') $isLs6 = true;
          @endphp
          <div class="gld-main-container">
              <livewire:table.search-table
                  id="{{ $id }}"
                  :$pagination
                  :$expandable
                  :$page
                  :paginationData="[]"
                  :title="$noticeType"
                  :notices="$noticeGroup"
                  :column="$this->column"
                  :isLs6="$isLs6"
              />
          </div>
        @endforeach
    @endif
</section>
