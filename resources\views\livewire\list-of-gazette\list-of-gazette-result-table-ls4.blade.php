<?php
$locale = app()->getLocale();
?>
<div class="gld-search-table-wrapper" xmlns:livewire="http://www.w3.org/1999/html">
    <!-- @if(isset($title) && !empty($title))
        <div class="section-header toggle-btn">
            <h2 class="gld-highlight-title h2">{{ $title }}</h2>
        </div>
    @endif -->
    {{-- expand-container --}}
    <div class="gld-default-table-container @if($expandable) expand-container @endif gld-search-table-container {{ $isShow ? 'open' : 'hide' }}">
        <div class="gld-search-table">
            @if (count($notices) == 0)
                <div class="table-empty-result">
                    -- {{ __('general.table.no_match') }} --
                </div>
            @else
                @foreach ($notices as $notice)
                  @if ($notice->file_ind != 'Y')
                      <div class="gld-search-table-row">
                          <div class="left label2">{{ __('general.table.columns_date') }}</div>
                          <div class="right label1">
                              @if ($locale == 'en')
                                  {{ $notice->enDayOfWeek }}, {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }} {{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }}
                              @else
                                  {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ $notice->zhDayOfWeek }}<br/>{{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }} {{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }}
                              @endif
                          </div>
                      </div>
                      <div class="gld-search-table-row">
                          <div class="left label2">{{ __('general.table.columns_title') }}</div>
                          <div class="right label1">
                                @if ($locale == 'en')
                                    <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls4', 'locale' => app()->getLocale(), 'parent' => $notice->id]) }}">{!! $notice->e_title !!}</a>
                                @else
                                    <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls4', 'locale' => app()->getLocale(), 'parent' => $notice->id]) }}">{!! $notice->c_title !!}</a>
                                @endif
                          </div>
                      </div>
                      <div class="gld-search-table-row">
                          <div class="left label2">{{ __('general.table.columns_eng') }}</div>
                          <div class="right label1">
                              --
                          </div>
                      </div>
                      <div class="gld-search-table-row">
                          <div class="left label2">{{ __('general.table.columns_chi') }}</div>
                          <div class="right label1">
                              --
                          </div>
                      </div>
                  @else
                      <div class="gld-search-table-row">
                          <div class="left label2">{{ __('general.table.columns_date') }}</div>
                          <div class="right label1">
                              @if ($locale == 'en')
                                  {{ $notice->enDayOfWeek }}, {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }} {{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }}
                              @else
                                  {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ $notice->zhDayOfWeek }}<br/>{{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }} {{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }}
                              @endif
                          </div>
                      </div>
                      <div class="gld-search-table-row">
                          <div class="left label2">{{ __('general.table.columns_title') }}</div>
                          <div class="right label1">
                              <div class="title label1">
                                  @if ($notice->table_name == 'ls6')
                                      {{ __('general.search_gazette.group_title_' . $notice->e_title) }}
                                  @else
                                      @if ($locale == 'en')
                                          {!! $notice->e_title !!}
                                      @else
                                          {!! $notice->c_title !!}
                                      @endif
                                  @endif
                              </div>
                              <div class="disc label2">
                                  @if (($notice->highlight ?? '') != '')
                                      {!! $notice->highlight !!}
                                  @endif
                              </div>
                          </div>
                      </div>
                      <div class="gld-search-table-row">
                          <div class="left label2">{{ __('general.table.columns_eng') }}</div>
                          <div class="right label1">
                              <a class="gld-svg-container" href="{{ $notice->englishPdfUrl }}">
                                  <img src="{{ asset('images/file/pdf.svg') }}" alt="{{ __('general.table.download_pdf_en') }}" title="{{ __('general.table.download_pdf_en') }}">
                              </a>
                          </div>
                      </div>
                      <div class="gld-search-table-row">
                          <div class="left label2">{{ __('general.table.columns_chi') }}</div>
                          <div class="right label1">
                              <a class="gld-svg-container" href="{{ $notice->chinesePdfUrl }}">
                                  <img src="{{ asset('images/file/pdf.svg') }}" alt="{{ __('general.table.download_pdf_tc') }}" title="{{ __('general.table.download_pdf_tc') }}">
                              </a>
                          </div>
                      </div>
                  @endif
                @endforeach
            @endif
        </div>
        <table class="gld-default-table">
            <thead>
                <tr>
                    <th class="gld-default-table-header label2" style="min-width: 200px">{{ __('general.table.columns_date') }}</th>
                    <th class="gld-default-table-header label2" style="min-width: 460px; width: 100%">{{ __('general.table.columns_title') }}</th>
                    <th class="gld-default-table-header label2" style="min-width: 100px">{{ __('general.table.columns_eng') }}</th>
                    <th class="gld-default-table-header label2" style="min-width: 100px">{{ __('general.table.columns_chi') }}</th>
                </tr>
            </thead>
            <tbody>
            @if (count($notices) == 0)
                <tr>
                    <td colspan="5">
                        <div class="table-empty-result">
                            -- {{ __('general.table.no_match') }} --
                        </div>
                    </td>
                </tr>
            @else
                @foreach ($notices as $notice)
                    @if ($notice->file_ind != 'Y')
                        <tr>
                            <td class="gld-default-table-data label1">
                                @if ($locale == 'en')
                                    {{ $notice->enDayOfWeek }}, {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }} {{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }}
                                @else
                                    {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ $notice->zhDayOfWeek }}<br/>{{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }} {{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }}
                                @endif
                            </td>
                            <td class="gld-default-table-data label1">
                                <div class="title label1">
                                    @if ($locale == 'en')
                                        <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls4', 'locale' => app()->getLocale(), 'parent' => $notice->id]) }}">{!! $notice->e_title !!}</a>
                                    @else
                                        <a href="{{ route('list-of-gazette-result', ['id' => $gazette->id, 'category' => 'ls4', 'locale' => app()->getLocale(), 'parent' => $notice->id]) }}">{!! $notice->c_title !!}</a>
                                    @endif
                                </div>
                            </td>
                            <td class="gld-default-table-data label1">
                                --
                            </td>
                            <td class="gld-default-table-data label1">
                                --
                            </td>
                        </tr>
                    @else
                        <tr>
                            <td class="gld-default-table-data label1">
                                @if ($locale == 'en')
                                    {{ $notice->enDayOfWeek }}, {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }} {{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }}
                                @else
                                    {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ $notice->zhDayOfWeek }}<br/>{{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }} {{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }}
                                @endif
                            </td>
                            <td class="gld-default-table-data label1">
                                <div class="title label1">
                                    @if ($notice->table_name == 'ls6')
                                        {{ __('general.search_gazette.group_title_' . $notice->e_title) }}
                                    @else
                                        @if ($locale == 'en')
                                            {!! $notice->e_title !!}
                                        @else
                                            {!! $notice->c_title !!}
                                        @endif
                                    @endif
                                </div>
                                <div class="disc label2">
                                    @if (($notice->highlight ?? '') != '')
                                        {!! $notice->highlight !!}
                                    @endif
                                </div>
                            </td>
                            <td class="gld-default-table-data label1">
                                <a class="gld-svg-container" href="{{ $notice->englishPdfUrl }}" target="_blank">
                                    <img src="{{ asset('images/file/pdf.svg') }}" alt="{{ __('general.table.download_pdf_en') }}" title="{{ __('general.table.download_pdf_en') }}">
                                </a>
                            </td>
                            <td class="gld-default-table-data label1">
                                <a class="gld-svg-container" href="{{ $notice->chinesePdfUrl }}" target="_blank">
                                    <img src="{{ asset('images/file/pdf.svg') }}" alt="{{ __('general.table.download_pdf_tc') }}" title="{{ __('general.table.download_pdf_tc') }}">
                                </a>
                            </td>
                        </tr>
                    @endif
                @endforeach
            @endif
            </tbody>
        </table>
    </div>
</div>