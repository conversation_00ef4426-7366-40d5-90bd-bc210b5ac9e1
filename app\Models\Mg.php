<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Mg extends Model
{
    use SoftDeletes;

    protected $table = 'mg';
    protected $primaryKey = 'id';

    public function department()
    {
        return $this->belongsTo(Department::class, 'dept_id');
    }

    public function typeDetail()
    {
        return $this->belongsTo(Type::class, 'type');
    }
}
