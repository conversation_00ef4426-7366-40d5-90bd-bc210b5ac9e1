.error-message {
    color: var(--warning-600);
    padding: var(--spacing-md) var(--spacing-sm);
}

.clear-input-icon::after {
    mask: url("/images/icon/cancel.svg") no-repeat;
    mask-size: cover;
}

.search-icon::after {
    mask: url("/images/icon/search.svg") no-repeat;
    mask-size: cover;
}

.number-input,
.text-input {
    height: 40px;
    border-radius: 6px;
    border: 1px solid var(--grey-200);
    padding: 14px;
}

.number-input:hover,
.text-input:hover {
    box-shadow: var(--shadow-base);
}

.number-input:focus,
.text-input:focus {
    outline: none;
    border: 1px solid var(--primary-600);
}

input[type="checkbox"] {
    position: absolute;
    width: 0;
    height: 0;
    visibility: hidden;
}

input[type="checkbox"]+label .checkmark {
    min-width: 20px;
    width: 20px;
    height: 20px;
    border-radius: var(--radius-x-sm);
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid var(--grey-200);
}


input[type="checkbox"]:checked+label .checkmark {
    background: var(--primary-600);
    border: none;
}

input[type="checkbox"]:checked+label .checkmark::after {
    display: inline-block;
    content: "";
    mask: url("/images/icon/tick.svg") no-repeat;
    mask-size: contain;
    background-color: var(--grey-white);
    width: 10px;
    height: 8px;
}

.checkbox-container {
    position: relative;
}

.default-select-dropdown-container {
    position: relative;
}

.default-select-dropdown-container .select-dropdown-label {
    display: block;
    color: var(--grey-600);
    margin-bottom: var(--spacing-x-sm);
}

.select-dropdown-menu {
    margin-top: var(--spacing-xx-sm);
    position: absolute;
    background-color: var(--grey-white);
    height: fit-content;
    z-index: 12;
    width: 100%;
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-sm);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease;
}

.select-dropdown-menu.show {
    visibility: visible;
    opacity: 1;
}

.select-dropdown-menu ul {
    padding: var(--spacing-x-sm);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xx-sm);
}

.select-dropdown-menu .default-select-dropdown-items {
    padding: var(--spacing-x-sm) var(--spacing-md);
    cursor: pointer;
}

.select-dropdown-menu .default-select-dropdown-items::before {
    display: inline-block;
    content: "";
    width: 10px;
    height: 9px;
    margin-right: var(--spacing-x-sm);
}

.select-dropdown-menu .default-select-dropdown-items.selected::before {
    display: inline-block;
    content: "";
    mask: url("/images/icon/tick.svg") no-repeat;
    mask-size: contain;
    width: 10px;
    height: 9px;
    background-color: var(--grey-600);
    margin-right: var(--spacing-x-sm);
}

.select-dropdown-menu .default-select-dropdown-items:hover {
    background-color: var(--primary-50);
}

.select-dropdown-menu .checkbox-select-dropdown-items {
    width: 100%;
}

.select-dropdown-menu .checkbox-select-dropdown-items label {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    cursor: pointer;
    position: relative;
    padding: var(--spacing-x-sm) var(--spacing-md);
    width: 100%;
}

.select-dropdown-menu .checkbox-select-dropdown-items label .checkmark {
    border-radius: 2px;
}

.select-dropdown-menu .checkbox-select-dropdown-item:hover {
    background-color: var(--primary-50);
}

.white-select-dropdown-button,
.default-select-dropdown-button {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    border-radius: 6px;
    border: 1px solid var(--grey-200);
    background: var(--grey-white);
    background: var(--grey-white);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 0.75rem;
    line-height: 130%;
    letter-spacing: 0;
    font-weight: 600;
    height: 40px;
    cursor: pointer;
}

.white-select-dropdown-button:focus,
.default-select-dropdown-button:focus {
    outline: none;
    border: 1px solid var(--primary-600);
}

.white-select-dropdown-button:hover,
.default-select-dropdown-button:hover {
    box-shadow: var(--shadow-base);
}

.white-select-dropdown-button:disabled,
.default-select-dropdown-button:disabled {
    background: var(--grey-50);
    color: var(--grey-600);
    cursor: not-allowed;
}

.white-select-dropdown-button:disabled:hover,
.default-select-dropdown-button:disabled:hover {
    box-shadow: none;
}


.white-select-dropdown-button {
    padding: var(--spacing-x-sm);
    padding-left: var(--spacing-md);
    gap: var(--spacing-x-sm);
}

.white-select-dropdown-button::after {
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
    content: url("/images/icon/select-arrows.svg");
    width: 20px;
    height: 20px;
    color: var(--grey-600);
}

.default-select-dropdown-button {
    padding-left: var(--spacing-sm);
    padding-right: 45px;
    -webkit-padding-end: 45px;
}

.default-select-dropdown-button::after {
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
    content: url("/images/icon/select-arrows.svg");
    position: absolute;
    width: 40px;
    height: 100%;
    background: var(--grey-50);
    border-radius: 0 6px 6px 0;
    border-left: 1px solid var(--grey-200);
    right: 0;
}

.default-select-dropdown-button:focus-within::after {
    border-left: 1px solid var(--primary-600);
    background-color: var(--primary-50);
}


.text-select-dropdown-button {
    background: none;
    border: none;
    padding: var(--spacing-sm) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-xx-sm);
    color: var(--primary-600);
}

.text-select-dropdown-button .dropdown-button-text {
    color: var(--primary-600);
}

.text-select-dropdown-button .dropdown-button-text:hover {
    color: var(--primary-800);
}

.text-select-dropdown-button:hover {
    color: var(--primary-800);
}

.text-select-dropdown-button::after {
    display: inline-block;
    content: "";
    mask: url("/images/icon/select-arrows.svg") no-repeat;
    mask-size: contain;
    width: 9px;
    height: 5px;
    background-color: currentColor;
}


@media (min-width: 768px) {
    .default-select-dropdown-button {
        height: 48px;
    }
}
