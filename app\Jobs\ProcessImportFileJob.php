<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

use App\Models\WebsiteContent;
use App\Models\Gazette;
use App\Models\Mg;
use App\Models\Ls1;
use App\Models\Ls2;
use App\Models\Ls3;
use App\Models\Ls4;
use App\Models\Ls5;
use App\Models\Ls6;
use App\Models\Department;
use App\Models\Type;

use App\Models\ElasticSearch;

class ProcessImportFileJob implements ShouldQueue
{
    public $timeout = 3600;

    use InteractsWithQueue, Queueable, SerializesModels;

    protected $filePath;

    /**
     * Create a new job instance.
     */
    public function __construct($filePath)
    {
        $this->filePath = $filePath;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("" . __METHOD__ . " - Processing file: {$this->filePath}");

        $baseDirectory = storage_path('app/private/import/');
        $archiveDirectory = $baseDirectory . 'Archived/';

        $filename = basename($this->filePath);
        if (strpos($filename, 'gazette_') !== false) {
            Log::info("importing gazette data from file: " . $filename);
            $result = $this->handleGazetteNoticesImport($this->filePath);
        } else if (strpos($filename, 'website_contents_') !== false) {
            Log::info("importing website content data from file: " . $filename);
            $result = $this->handleWebsiteContentsImport($this->filePath);
        } else if (strpos($filename, 'types_') !== false) {
            Log::info("importing types data from file: " . $filename);
            $result = $this->handleTypesImport($this->filePath);
        } else if (strpos($filename, 'departments_') !== false) {
            Log::info("importing departments data from file: " . $filename);
            $result = $this->handleDepartmentsImport($this->filePath);
        } else {
            Log::error("Error reading file: " . $filename . " - unknown file type");
            return;
        }

        if ($result != false) {
            Log::info("finished importing file: " . $filename);
            rename($this->filePath, $archiveDirectory . $filename);
        } else {
            Log::info("error processing file: " . $filename);
        }
    }

    private function handleGazetteNoticesImport($filePath)
    {
        try {
            ElasticSearch::createSnapShot();
            $fileContents = file_get_contents($filePath);
            $content = json_decode($fileContents, true);

            if (isset($content['gazette'])) {
                $gazetteContent = $content['gazette'];

                // check if the gazette exist, modify if it does, create if it doesn't
                $gazette = Gazette::where('dateno', $gazetteContent['dateno'])
                    ->where('gno', $gazetteContent['gno'])
                    ->where('volume', $gazetteContent['volume'])
                    ->where('extra', $gazetteContent['extra'])
                    ->first();
                if ($gazette) {
                    $gazette->dateno = $gazetteContent['dateno'];
                    $gazette->year = $gazetteContent['year'];
                    $gazette->gno = $gazetteContent['gno'];
                    $gazette->volume = $gazetteContent['volume'];
                    $gazette->extra = $gazetteContent['extra'];
                    $gazette->status = $gazetteContent['status'];

                    $gazette->save();
                } else {
                    $gazette = new Gazette();
                    $gazette->dateno = $gazetteContent['dateno'];
                    $gazette->year = $gazetteContent['year'];
                    $gazette->gno = $gazetteContent['gno'];
                    $gazette->volume = $gazetteContent['volume'];
                    $gazette->extra = $gazetteContent['extra'];
                    $gazette->status = $gazetteContent['status'];

                    $gazette->save();
                }

                // then mg
                $mgContents = $content['mg'] ?? [];
                Log::info($mgContents);
                if (count($mgContents)  > 0) {
                    // firstly we remove all mg records for this gazette
                    Mg::where('gazette_id', $gazette->id)->delete();
                    ElasticSearch::deleteDocumentsByGazetteIdAndNoticeType($gazette, 'mg');

                    foreach ($mgContents as $mgContent) {
                        $mg = new Mg();
                        $mg->gazette_id = $gazette->id;
                        $mg->dateno = $mgContent['dateno'];
                        $mg->volume = $mgContent['volume'];
                        $mg->gno = $mgContent['gno'];
                        $mg->notice_no = $mgContent['notice_no'];
                        $mg->e_title = $mgContent['e_title'];
                        $mg->c_title = $mgContent['c_title'];
                        $mg->e_dept = $mgContent['e_dept'];
                        $mg->c_dept = $mgContent['c_dept'];
                        $mg->e_writer = $mgContent['e_writer'];
                        $mg->c_writer = $mgContent['c_writer'];
                        $mg->type = $mgContent['type'];
                        $mg->e_position = $mgContent['e_position'];
                        $mg->c_position = $mgContent['c_position'];
                        $mg->url = $mgContent['url'];
                        $mg->status = $mgContent['status'];
                        $mg->dept_id = $mgContent['dept_id'];
                        $mg->repost = $mgContent['repost'];
                        $mg->chapter = $mgContent['chapter'];

                        $mg->save();
                    }

                    ElasticSearch::indexDocumentsByGazetteAndNoticeType($gazette, 'mg');
                }

                // then ls1
                $ls1Contents = $content['ls1'] ?? [];
                if (count($ls1Contents) > 0) {
                    // firstly we remove all ls1 records for this gazette
                    Ls1::where('gazette_id', $gazette->id)->delete();
                    ElasticSearch::deleteDocumentsByGazetteIdAndNoticeType($gazette, 'ls1');

                    foreach ($ls1Contents as $ls1Content) {
                        $ls1 = new Ls1();
                        $ls1->gazette_id = $gazette->id;
                        $ls1->dateno = $ls1Content['dateno'];
                        $ls1->volume = $ls1Content['volume'];
                        $ls1->gno = $ls1Content['gno'];
                        $ls1->notice_no = $ls1Content['notice_no'];
                        $ls1->e_title = $ls1Content['e_title'];
                        $ls1->c_title = $ls1Content['c_title'];
                        $ls1->e_dept = $ls1Content['e_dept'];
                        $ls1->c_dept = $ls1Content['c_dept'];
                        $ls1->e_writer = $ls1Content['e_writer'];
                        $ls1->c_writer = $ls1Content['c_writer'];
                        $ls1->type = $ls1Content['type'];
                        $ls1->e_position = $ls1Content['e_position'];
                        $ls1->c_position = $ls1Content['c_position'];
                        $ls1->url = $ls1Content['url'];
                        $ls1->status = $ls1Content['status'];
                        $ls1->dept_id = $ls1Content['dept_id'];
                        $ls1->repost = $ls1Content['repost'];
                        $ls1->chapter = $ls1Content['chapter'];

                        $ls1->save();
                    }

                    ElasticSearch::indexDocumentsByGazetteAndNoticeType($gazette, 'ls1');
                }
                
                // then ls2
                $ls2Contents = $content['ls2'] ?? [];
                if (count($ls2Contents) > 0) {
                    // firstly we remove all ls2 records for this gazette
                    Ls2::where('gazette_id', $gazette->id)->delete();
                    ElasticSearch::deleteDocumentsByGazetteIdAndNoticeType($gazette, 'ls2');

                    foreach ($ls2Contents as $ls2Content) {
                        $ls2 = new Ls2();
                        $ls2->gazette_id = $gazette->id;
                        $ls2->dateno = $ls2Content['dateno'];
                        $ls2->volume = $ls2Content['volume'];
                        $ls2->gno = $ls2Content['gno'];
                        $ls2->notice_no = $ls2Content['notice_no'];
                        $ls2->e_title = $ls2Content['e_title'];
                        $ls2->c_title = $ls2Content['c_title'];
                        $ls2->e_dept = $ls2Content['e_dept'];
                        $ls2->c_dept = $ls2Content['c_dept'];
                        $ls2->e_writer = $ls2Content['e_writer'];
                        $ls2->c_writer = $ls2Content['c_writer'];
                        $ls2->type = $ls2Content['type'];
                        $ls2->e_position = $ls2Content['e_position'];
                        $ls2->c_position = $ls2Content['c_position'];
                        $ls2->url = $ls2Content['url'];
                        $ls2->status = $ls2Content['status'];
                        $ls2->dept_id = $ls2Content['dept_id'];
                        $ls2->repost = $ls2Content['repost'];
                        $ls2->chapter = $ls2Content['chapter'];

                        $ls2->save();
                    }

                    ElasticSearch::indexDocumentsByGazetteAndNoticeType($gazette, 'ls2');
                }

                // then ls3
                $ls3Contents = $content['ls3'] ?? [];
                if (count($ls3Contents) > 0) {
                    // firstly we remove all ls3 records for this gazette
                    Ls3::where('gazette_id', $gazette->id)->delete();
                    ElasticSearch::deleteDocumentsByGazetteIdAndNoticeType($gazette, 'ls3');

                    foreach ($ls3Contents as $ls3Content) {
                        // check if the ls3 exist, modify if it does, create if it doesn't
                        $ls3 = Ls3::where('gazette_id', $gazette->id)
                        ->where('notice_no', $ls3Content['notice_no'])
                        ->first();

                        $ls3 = new Ls3();
                        $ls3->gazette_id = $gazette->id;
                        $ls3->dateno = $ls3Content['dateno'];
                        $ls3->volume = $ls3Content['volume'];
                        $ls3->gno = $ls3Content['gno'];
                        $ls3->notice_no = $ls3Content['notice_no'];
                        $ls3->e_title = $ls3Content['e_title'];
                        $ls3->c_title = $ls3Content['c_title'];
                        $ls3->e_dept = $ls3Content['e_dept'];
                        $ls3->c_dept = $ls3Content['c_dept'];
                        $ls3->e_writer = $ls3Content['e_writer'];
                        $ls3->c_writer = $ls3Content['c_writer'];
                        $ls3->type = $ls3Content['type'];
                        $ls3->e_position = $ls3Content['e_position'];
                        $ls3->c_position = $ls3Content['c_position'];
                        $ls3->url = $ls3Content['url'];
                        $ls3->status = $ls3Content['status'];
                        $ls3->dept_id = $ls3Content['dept_id'];
                        $ls3->repost = $ls3Content['repost'];
                        $ls3->chapter = $ls3Content['chapter'];

                        $ls3->save();
                    }

                    ElasticSearch::indexDocumentsByGazetteAndNoticeType($gazette, 'ls3');
                }

                // then ls4
                $ls4Contents = $content['ls4'] ?? [];
                if (count($ls4Contents) > 0) {
                    // firstly we remove all ls4 records for this gazette
                    Ls4::where('dateno', $gazette->dateno)
                    ->where('volume', $gazette->volume)
                    ->where('gno', $gazette->gno)
                    ->delete();

                    $ls4Parents = [];

                    foreach ($ls4Contents as $ls4Content) {
                        $ls4 = new Ls4();
                        $ls4->dateno = $ls4Content['dateno'];
                        $ls4->volume = $ls4Content['volume'];
                        $ls4->gno = $ls4Content['gno'];
                        $ls4->level = $ls4Content['level'];
                        $ls4->e_level_title = $ls4Content['e_level_title'];
                        $ls4->c_level_title = $ls4Content['c_level_title'];
                        $ls4->ordering = $ls4Content['ordering'];
                        $ls4->p_level_id = $ls4Parents[$ls4Content['p_level_id']] ?? null;
                        $ls4->file_ind = $ls4Content['file_ind'];
                        $ls4->file_id = $ls4Content['file_id'];
                        $ls4->e_filename = $ls4Content['e_filename'];
                        $ls4->c_filename = $ls4Content['c_filename'];
                        $ls4->deleted_at = $ls4Content['deleted_at'];

                        $ls4->save();

                        $ls4Parents[$ls4Content['id']] = $ls4->id;
                    }

                    ElasticSearch::indexDocumentsByGazetteAndNoticeType($gazette, 'ls4');
                }

                // then ls5
                $ls5Contents = $content['ls5'] ?? [];
                if (count($ls5Contents) > 0) {
                    // firstly we remove all ls5 records for this gazette
                    Ls5::where('gazette_id', $gazette->id)->delete();
                    ElasticSearch::deleteDocumentsByGazetteIdAndNoticeType($gazette, 'ls5');

                    foreach ($ls5Contents as $ls5Content) {
                        // check if the ls5 exist, modify it if it does, create if it doesn't
                        $ls5 = new Ls5();
                        $ls5->gazette_id = $gazette->id;
                        $ls5->dateno = $ls5Content['dateno'];
                        $ls5->volume = $ls5Content['volume'];
                        $ls5->gno = $ls5Content['gno'];
                        $ls5->notice_no = $ls5Content['notice_no'];
                        $ls5->e_title = $ls5Content['e_title'];
                        $ls5->c_title = $ls5Content['c_title'];
                        $ls5->e_dept = $ls5Content['e_dept'];
                        $ls5->c_dept = $ls5Content['c_dept'];
                        $ls5->e_writer = $ls5Content['e_writer'];
                        $ls5->c_writer = $ls5Content['c_writer'];
                        $ls5->type = $ls5Content['type'];
                        $ls5->e_position = $ls5Content['e_position'];
                        $ls5->c_position = $ls5Content['c_position'];
                        $ls5->url = $ls5Content['url'];
                        $ls5->status = $ls5Content['status'];
                        $ls5->dept_id = $ls5Content['dept_id'];
                        $ls5->repost = $ls5Content['repost'];
                        $ls5->chapter = $ls5Content['chapter'];

                        $ls5->save();
                    }

                    ElasticSearch::indexDocumentsByGazetteAndNoticeType($gazette, 'ls5');
                }

                // then ls6
                $ls6Contents = $content['ls6'] ?? [];
                if (count($ls6Contents) > 0) {
                    // firstly we remove all ls6 records for this gazette
                    Ls6::where('dateno', $gazette->dateno)
                    ->where('volume', $gazette->volume)
                    ->where('gno', $gazette->gno)
                    ->delete();
                    ElasticSearch::deleteDocumentsByGazetteIdAndNoticeType($gazette, 'ls6');

                    foreach ($ls6Contents as $ls6Content) {
                        $ls6 = new Ls6();
                        $ls6->dateno = $ls6Content['dateno'];
                        $ls6->volume = $ls6Content['volume'];
                        $ls6->gnos = $ls6Content['gnos'];
                        $ls6->group = $ls6Content['group'];
                        $ls6->status = $ls6Content['status'];

                        $ls6->save();
                    }

                    ElasticSearch::indexDocumentsByGazetteAndNoticeType($gazette, 'ls6');
                }

                // $indexResult = ElasticSearch::indexDocumentsByGazette($gazette);

            } else {
                Log::error("Error reading file: " . $filePath . " - no gazette datfilePatha found");
                return false;
            }

            return true;
        } catch (\Exception $e) {
            Log::error("Error reading file: " . $filePath . " - " . $e->getMessage());
            return false;
        }
    }

    private function handleWebsiteContentsImport($filePath)
    {
        try {
            $fileContents = file_get_contents($filePath);
            $contents= json_decode($fileContents, true);

            if (isset($contents['contents'])) {
                foreach ($contents as $content) {
                    // check if the website exist, modify it if does, create if it doesn't
                    $websiteContent = WebsiteContent::where('page_name', $content['page_name'])
                      ->where('key', $content['key'])->first();

                    if ($websiteContent) {
                        $websiteContent->page_name = $content['page_name'];
                        $websiteContent->key = $content['key'];
                        $websiteContent->en_content = $content['en_content'];
                        $websiteContent->zh_content = $content['zh_content'];

                        $websiteContent->save();
                    } else {
                        $websiteContent = new WebsiteContent();
                        $websiteContent->page_name = $content['page_name'];
                        $websiteContent->key = $content['key'];
                        $websiteContent->en_content = $content['en_content'];
                        $websiteContent->zh_content = $content['zh_content'];

                        $websiteContent->save();
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error("Error reading file: " . $filePath . " - " . $e->getMessage());
        }
    }

    private function handleTypesImport($filePath)
    {
        try {
            $fileContents = file_get_contents($filePath);
            $contents= json_decode($fileContents, true);

            if (isset($contents['types'])) {
                foreach ($contents as $content) {
                    // check if the type exist, modify it if does, create if it doesn't
                    $type = Type::where('id', $content['id'])->first();

                    if ($type) {
                        $type->e_type = $content['e_type'];
                        $type->c_type = $content['c_type'];
                        $type->ordering = $content['ordering'];

                        $type->save();
                    } else {
                        $type = new Type();
                        $type->id = $content['id'];
                        $type->e_type = $content['e_type'];
                        $type->c_type = $content['c_type'];
                        $type->ordering = $content['ordering'];

                        $type->save();
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error("Error reading file: " . $filePath . " - " . $e->getMessage());
        }
    }

    private function handleDepartmentsImport($filePath)
    {
        try {
            $fileContents = file_get_contents($filePath);
            $contents= json_decode($fileContents, true);

            if (isset($contents['departments'])) {
                foreach ($contents as $content) {
                    // check if the department exist, modify it if does, create if it doesn't
                    $department = Department::where('id', $content['id'])->first();

                    if ($department) {
                        $department->e_dept = $content['e_dept'];
                        $department->c_dept = $content['c_dept'];
                        $department->ed_url = $content['ed_url'];
                        $department->cd_url = $content['cd_url'];
                        $department->e_writer = $content['e_writer'];
                        $department->c_writer = $content['c_writer'];
                        $department->e_position = $content['e_position'];
                        $department->c_position = $content['c_position'];

                        $department->save();
                    } else {
                        $department = new Department();
                        $department->id = $content['id'];
                        $department->e_dept = $content['e_dept'];
                        $department->c_dept = $content['c_dept'];
                        $department->ed_url = $content['ed_url'];
                        $department->cd_url = $content['cd_url'];
                        $department->e_writer = $content['e_writer'];
                        $department->c_writer = $content['c_writer'];
                        $department->e_position = $content['e_position'];
                        $department->c_position = $content['c_position'];

                        $department->save();
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error("Error reading file: " . $filePath . " - " . $e->getMessage());
        }
    }
}
