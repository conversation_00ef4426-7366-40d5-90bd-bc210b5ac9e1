@import url("../component/card/card.css");
@import url("../component/text/highlight-title.css");
@import url("../component/table/default-table.css");
@import url("../component/button/arrow-button.css");
@import url("../component/input/input.css");
@import url("../component/table/search-table.css");


.gld-main-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xx-lg);
}

#the-gov-hksar a {
    font-size: 0.875rem;
    line-height: 150%;
    letter-spacing: 0;
    font-weight: 600;
}

#the-gov-hksar .gld-card {
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
}


.list-of-gazette-table-section .gld-highlight-title {
    margin-bottom: var(--spacing-xxx-big);
}


.published-gazette-list {
    display: flex;
    flex-direction: column;
    padding-left: 0;
}

.published-gazette-link {
    list-style: none;
    padding: var(--spacing-xx-sm) 0;
}

#list-of-gazette-result-body .gld-highlight-title {
    margin-bottom: var(--spacing-md);
}


@media (min-width: 576px) {

    .published-gazette-link:not(:last-child) a:after {
        content: '';
        width: 1px;
        height: 100%;
        margin: 0 12px;
        border-right: 1px dashed var(--grey-400) ;
    }

    .published-gazette-list {
        flex-wrap: wrap;
        flex-direction: row;
    }
}

@media (min-width: 768px) {
    #the-gov-hksar .gld-card {
        padding: var(--spacing-xx-big) var(--spacing-xxx-lg);
    }

}

@media (min-width: 992px) {
    #the-gov-hksar a {
        font-size: 1.125rem;
        line-height: 150%;
        letter-spacing: 0;
        font-weight: 600;
    }

}



