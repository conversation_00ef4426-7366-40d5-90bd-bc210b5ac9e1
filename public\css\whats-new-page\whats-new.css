@import url("../component/card/card.css");
@import url("../component/text/highlight-title.css");
@import url("../component/table/default-table.css");
@import url("../component/button/arrow-button.css");
@import url("../contact-us-page/contact-us-icons.css");


#whats-new .gld-main-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xx-lg);
}

#whats-new .whats-new-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxx-big);
}

#whats-new .gld-card-title p {
    font-size: 1.375rem;
    line-height: 120%;
    letter-spacing: 0;
    font-weight: 400;
    font-family: "DM Serif Display", serif;
    color: var(--primary-800);
}

#whats-new .gld-default-table-header {
    min-width: 260px;
    max-width: 260px;
    width: 100%;
}

#resources-for-gazette .gld-card-title {
    color: var(--primary-800);
}

#resources-for-gazette .gld-card {
    display: flex;
    flex-direction: column;
    padding:  var(--spacing-big);
    box-shadow: var(--shadow-base);
    gap: var(--spacing-md);
}

#resources-for-gazette .main-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 32px;
}

#resources-for-gazette .sub-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 32px;
}

#pnsps-card {
    background: var(--primary-50);
    position: relative;
    overflow: hidden;
    z-index: 0;
}

#pnsps-card::after {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 180px;
    opacity: 0.1;
    z-index: -1;
    background: url("/images/whats-new/pattern-bg.png") var(--primary-50) 50% / cover no-repeat;
}

#pnsps-card .gld-card-footer {
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}

#pnsps-card .gld-card-footer .gld-img-container {
    width: 180px;
    height: auto;
}

.gld-default-table-data span,
.gld-default-table-data p {
    color: var(--grey-600);
}

.purchase-icon-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-x-sm);
    font-weight: 600;
}

.purchase-icon-container .gld-svg-container {
    width: 24px;
    min-width: 24px;
    height: 24px;
    color: var(--primary-800);
}


@media (min-width: 768px) {
    #resources-for-gazette {
        .sub-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
}

@media (min-width: 992px) {
    #whats-new {
        .gld-default-table-header {
            min-width: 316px;
            max-width: 316px;
        }
    }

    #resources-for-gazette {
        .gld-card {
            padding: var(--spacing-xx-big) var(--spacing-xxx-big);
        }
    }

    #pnsps-card {
        .gld-card-footer {
            .gld-img-container {
                width: 226px;
                height: 85px;
            }
        }

        &::after {
            height: 331px;
        }
    }

    #whats-new .gld-card-title p {
        font-size: 1.875rem;
        line-height: 120%;
        letter-spacing: 0;
        font-weight: 400;
        color: var(--primary-800);
    }

}

@media (min-width: 1200px) {
    #resources-for-gazette {
        .main-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .sub-grid {
            grid-template-columns: 1fr;
            grid-template-rows: repeat(2, fit-content(100%));
        }
    }
}

@media print {
    /*#resources-for-gazette {*/
    /*    margin-top: var(--spacing-xxx-lg);*/
    /*}*/

    /*#pnsps-card {*/
    /*    background: var(--grey-white) !important;*/
    /*}*/

    /*#pnsps-card .gld-card-footer {*/
    /*    display: none;*/
    /*}*/
}

