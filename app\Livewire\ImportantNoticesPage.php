<?php

namespace App\Livewire;

use Livewire\Component;

class ImportantNoticesPage extends Component
{
    public $pageTitle;
    public $showCaptcha = false;
    
    public function mount()
    {
        $this->pageTitle = 'Important Notices';
        $this->showCaptcha = request()->showCaptcha ?? false;
    }

    public function render()
    {
        $pageTitle = $this->pageTitle;
        return view('important-notices-page', compact('pageTitle'));
    }
}
