@import url("./search-bar.css");
@import url("./search-period.css");
@import url("../component/input/input.css");

#search-gazette-result .gld-main-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-big);
}


.search-gazette-filter-wrapper {
    background: var(--grey-white);
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-base);
}

.search-gazette-filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-x-sm) var(--spacing-big);
    cursor: pointer;
}

.search-gazette-filter-header:hover button {
    background: var(--primary-100);
}

.search-gazette-filter-header .search-gazette-filter-title {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.search-gazette-filter-header .gld-svg-container {
    width: 20px;
    height: 20px;
    color: var(--grey-600);
}

.search-gazette-filter-header h2 {
    color: var(--grey-600);
}

.search-gazette-filter-options {
    border-top: 1px solid var(--grey-200);
}

.search-gazette-filter-options .search-gazette-filter-options-container {
    padding: var(--spacing-big);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxx-big);
}

.search-gazette-filter-options .filter-option-label {
    color: var(--grey-600);
}


.filter-options-category-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.search-gazette-category-button {
    position: relative;
    height: 40px;
}

.search-gazette-category-button label {
    cursor: pointer;
    width: 100%;
    height: 100%;
    padding: var(--spacing-md) var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: 11px;
    border: 2px dashed var(--grey-200);
    border-radius: var(--radius-sm);
}

.search-gazette-category-button label .checkmark {
    border: none !important;
}

.search-gazette-category-button input:checked + label {
    border: 1px solid var(--primary-600);
    background: var(--primary-50);
}

.search-gazette-category-button .category-total {
    margin-left: auto;
    color: var(--grey-600);
}

.search-gazette-category-button:hover {
    background-color: var(--grey-50);
    transition: background-color 0.2s ease;
}

.search-gazette-filter-buttons {
    height: 40px;
    display: flex;
    justify-content: space-between;
}

.search-gazette-filter-buttons .gld-svg-container {
    width: 20px;
    height: 20px;
    color: var(--grey-white);
}

.search-gazette-filter-buttons .gld-button {
    display: flex;
    gap: 6px;
    padding-left: var(--spacing-x-sm);
    padding-right: var(--spacing-md);
}

.search-gazette-filter-buttons button {
    height: 100%;
}

.filter-option-label {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-x-sm);
}

#filter-options-category{
    display: flex;
    flex-direction: column;
    gap: var(--spacing-x-sm);
}

.filter-icon::after {
    mask: url("/images/icon/filter.svg") no-repeat;
    mask-size: cover;
}

.filter-selection {
    display: flex;
    /*justify-content: end;*/
    flex-direction: column;
    gap: var(--spacing-md);
}

.search-gazette-result-table-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-gazette-result-table-filter .select-dropdown-menu {
    max-width: 306px;
    width: max-content;
}

.search-gazette-result-table-filter .default-select-dropdown-dropdown-set {
    display: flex;
    gap: var(--spacing-xx-sm);
    align-items: center;
}

.search-gazette-result-table-filter .default-select-dropdown-dropdown-set .select-dropdown-label {
    margin: 0;
}

.search-gazette-result-table-filter .table-order-by-filter .select-dropdown-menu {
    right: 0 !important;
}


.search-gazette-result-table-filter #dropdown-table-column-filter dropdown-button-text {
    display: none;
}

.search-gazette-result-table-filter #dropdown-table-column-filter::before {
    display: inline-block;
    content: "";
    width: 24px;
    height: 24px;
    background-color: currentColor;
    mask: url("/images/icon/column_filter.svg") no-repeat;
    mask-size: contain;
}


@media (min-width: 768px) {
    .search-gazette-filter-options .search-gazette-filter-options-container {
        gap: var(--spacing-lg);
    }


    .filter-options-category-container {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }

    .filter-options-category-container  .search-gazette-category-button {
       width: calc(50% - (var(--spacing-md) / 2));
        height: 56px;
    }

    .filter-options-category-container  .search-gazette-category-button label {
        padding: var(--spacing-md);
    }

    .filter-selection {
        flex-wrap: wrap;
        flex-direction: row;
        /*column-gap: var(--spacing-big);*/
        row-gap: var(--spacing-lg);
    }

    .filter-selection .default-select-dropdown-container {
        width: calc(50% - (var(--spacing-big) + 96px / 2));
    }

    .filter-selection .default-select-dropdown-container {
        margin-left: 60px;
    }

    .search-gazette-result-table-filter#dropdown-table-column-filter .dropdown-button-text {
        display: flex;
    }
}

@media print {
    /*.default-select-dropdown-dropdown-set button {*/
    /*    display: flex !important;*/
    /*}*/

    /*.default-select-dropdown-dropdown-set button span {*/
    /*    display: flex !important;*/
    /*}*/
}

