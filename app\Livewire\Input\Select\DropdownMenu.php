<?php

namespace App\Livewire\Input\Select;

use Livewire\Attributes\Reactive;
use Livewire\Component;

class DropdownMenu extends Component
{
    public string $id = '';
    public string $label = '';
    public string $name = '';
    #[Reactive] public bool $open = false;
    public array $type = [];
    #[Reactive] public array $options = [];
    public string $updateFn = '';
    public array $selectedArray = [];
    #[Reactive] public $selected;

    public function mount(): void
    {
        if($this->type['options'] === 'checkbox') {
            $this->selectedArray = $this->selected ?? [];
        }
    }

    public function updateArray()
    {
        if($this->type['options'] === 'checkbox') {
            $this->selectedArray = $this->selected ?? [];
        }
    }

    public function updated()
    {
        if($this->type['options'] === 'checkbox') {
            $this->dispatch($this->updateFn, propertyName: $this->name, value: implode(',', $this->selectedArray));
        }
    }

    public function render()
    {
        return view('livewire.input.select.dropdown-menu');
    }
}
