@php
        $ariaLabel = $customRender ?: ($selected ? $options[$selected] : $firstOption);
@endphp

<button
        {{ $disabled ? 'disabled' : null }}
        type="button"
        class="label2 {{ $type['button'] }}-select-dropdown-button {{ $open ? 'open' : null }}"
        wire:click="$parent.toggleCollapsed"
        @if($open && $type['options'] !== 'checkbox') wire:click.outside="$parent.closeMenu" @endif
        id="dropdown-{{ $id }}"
        aria-haspopup="listbox"
        aria-expanded="{{ $open ? 'true' : 'false' }}"
        aria-label="{{ $aria_label?: $ariaLabel }}"
>
        <span class="dropdown-button-text">
                {{ $ariaLabel }}
        </span>
</button>
