:root {
  --offset: 100px;

  --header-height-sm: 52px;
  --header-height: 72px;
  /*color*/
  --primary-50: #eff6ff;
  --primary-100: #CFE4FF;
  --primary-400: #60a5fa;
  --primary-600: #2563eb;
  --primary-800: #1e40af;

  --secondary-100: #ffedd5;
  --secondary-500: #ea580c;
  --secondary-700: #7c2d12;

  --grey-white: #ffffff;
  --grey-50: #fafafa;
  --grey-200: #e5e5e5;
  --grey-400: #909090;
  --grey-600: #606060;
  --grey-900: #171717;

  --shades-700: #616161;

  --warning-50: #fee2e2;
  --warning-600: #dc2626;
  --warning-700: #b91c1c;

  --success-100: #d1fae5;
  --success-600: #22c55e;
  --success-700: #15803d;

  /*spacing*/
  --spacing-none: 0px;
  --spacing-xx-sm: 4px;
  --spacing-x-sm: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-big: 20px;
  --spacing-x-big: 24px;
  --spacing-xx-big: 28px;
  --spacing-xxx-big: 32px;
  --spacing-lg: 40px;
  --spacing-x-lg: 48px;
  --spacing-xx-lg: 64px;
  --spacing-xxx-lg: 80px;
  --spacing-container-sm: 20px;
  --spacing-container: 88px;
  --spacing-huge: 96px;
  --spacing-xx-huge: 128px;

  /*radius*/
  --radius-none: 0px;
  --radius-xx-sm: 2px;
  --radius-x-sm: 4px;
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-big: 16px;
  --radius-lg: 32px;
  --radius-full: 9999px;

  /*shadow*/
  --shadow-inner: 0px 2px 4px 0px rgba(0, 0, 0, 0.06);
  --shadow-base: 0px 0px 3px 0px rgba(37, 99, 235, 0.10), 0px 1px 2px 0px rgba(37, 99, 235, 0.06);
  --shadow-md: 0px 4px 6px -1px rgba(37, 99, 235, 0.10), 0px 2px 4px -1px rgba(37, 99, 235, 0.06);
  --shadow-lg: 0px 8px 15px -3px rgba(0, 0, 0, 0.1),
    0 2px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-2xl: 0px 13px 35px rgba(0, 0, 0, 0.2);

  /*blur backdrop-filter*/
  --blur-large: blur(7.5px);

  .display {
    font-size: 2.125rem; /* 34px */
    line-height: 110%;
    letter-spacing: 0;
    font-weight: 400;
    font-family: "DM Serif Display", serif;
  }

  .h1 {
    font-size: 1.75rem; /* 28px */
    line-height: 120%;
    letter-spacing: 0;
    font-weight: 400;
    font-family: "DM Serif Display", serif;
  }

  .h2 {
    font-size: 1.5rem; /* 24px */
    line-height: 120%;
    letter-spacing: 0;
    font-weight: 400;
    font-family: "DM Serif Display", serif;
  }

  .h3 {
    font-size: 1.375rem; /* 22px */
    line-height: 120%;
    letter-spacing: 0;
    font-weight: 400;
    font-family: "DM Serif Display", serif;
  }

  .subtitle1 {
    font-size: 1.25rem; /* 20px */
    line-height: 130%;
    letter-spacing: 0;
    font-weight: 600;
  }

  .subtitle2 {
    font-size: 1.125rem; /* 18px */
    line-height: 130%;
    letter-spacing: 0;
    font-weight: 600;
  }

  .label1 {
    font-size: 0.875rem; /* 14px */
    line-height: 130%;
    letter-spacing: 0;
    font-weight: 600;
  }

  .label2 {
    font-size: 0.75rem; /* 12px */
    line-height: 130%;
    letter-spacing: 0;
    font-weight: 600;
  }

  .body1 {
    font-size: 0.875rem; /* 14px */
    line-height: 150%;
    letter-spacing: 0;
    font-weight: 500;
  }

  .body1-text-link {
    font-size: 0.875rem; /* 14px */
    line-height: 150%;
    letter-spacing: 0;
    font-weight: 600;
  }

  .body2 {
    font-size: 0.75rem; /* 12px */
    line-height: 150%;
    letter-spacing: 0;
    font-weight: 500;
  }

  @media only screen and (min-width: 992px) {
    .display {
      font-size: 3rem; /* 48px */
      line-height: 110%;
      letter-spacing: 0;
      font-weight: 400;
    }

    .h1 {
      font-size: 2.5rem; /* 40px */
      line-height: 120%;
      letter-spacing: 0;
      font-weight: 400;
    }

    .h2 {
      font-size: 2.125rem; /* 34px */
      line-height: 120%;
      letter-spacing: 0;
      font-weight: 400;
    }

    .h3 {
      font-size: 1.875rem; /* 30px */
      line-height: 120%;
      letter-spacing: 0;
      font-weight: 400;
    }

    .subtitle1 {
      font-size: 1.625rem; /* 26px */
      line-height: 130%;
      letter-spacing: 0;
      font-weight: 600;
    }

    .subtitle2 {
      font-size: 1.375rem; /* 22px */
      line-height: 130%;
      letter-spacing: 0;
      font-weight: 600;
    }

    .label1 {
      font-size: 1.125rem; /* 18px */
      line-height: 130%;
      letter-spacing: 0;
      font-weight: 600;
    }

    .label2 {
      font-size: 1rem; /* 16px */
      line-height: 130%;
      letter-spacing: 0;
      font-weight: 600;
    }

    .body1 {
      font-size: 1.125rem; /* 18px */
      line-height: 150%;
      letter-spacing: 0;
      font-weight: 500;
    }

    .body1-text-link {
      font-size: 1.125rem; /* 18px */
      line-height: 150%;
      letter-spacing: 0;
      font-weight: 600;
    }

    .body2 {
      font-size: 0.875rem; /* 14px */
      line-height: 150%;
      letter-spacing: 0;
      font-weight: 500;
    }
  }
}
