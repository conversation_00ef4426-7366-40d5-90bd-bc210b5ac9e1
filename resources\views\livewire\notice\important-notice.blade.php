<?php
$appenv = config('app.env');
?>
<section class="gld-main-wrapper pages-body" id="important-notices-body">
    <div class="gld-main-container">
        <div class="important-notices-content">
            <form class="gld-card" id="important-notices-form">
                <article class="gld-card-article">
                    <section class="gld-card-article-section">
                        <h2 class="h2 gld-highlight-title gld-card-article-section-title">{!! $data[1]['section_1_title'] !!}</h2>
                        {!! $data[1]['section_1_content'] !!}
                    </section>
                    @if (count($data) > 1)
                        @foreach ($data as $index => $item)
                            @if (is_int($index) && $index > 1)
                                <section class="gld-card-article-section">
                                    <h2 class="h2 gld-highlight-title gld-card-article-section-title">{!! $item["section_{$index}_title"] !!}</h2>
                                    {!! $item["section_{$index}_content"] !!}
                                </section>
                            @endif
                        @endforeach
                    @endif
                    @if($showCaptcha)
                        <div class="checkbox-container">
                            <input type="checkbox" id="accept-notice" value="1" wire:model="acceptNotice"/>
                            <label for="accept-notice" class="accept-notice-checkbox label1">
                                <span class="checkmark"></span>
                               {!! $data['checkbox_text'] !!}
                            </label>
                            @error('acceptNoticeError')
                            <div class="error-message">
                                {{ $message }}
                            </div>
                            @enderror
                        </div>
                        <div class="gld-dashed-divider"></div>
                        <div class="cf-container">
                            @if ($appenv === 'local')
                                <div style="width:27.5%">
                                    <img src="http://10.22.191.110:443/images/cf-turnstile-banner.png">
                                </div>
                            @elseif ($appenv === 'production')
                                <div>
                                    <div wire:ignore>
                                        <x-turnstile
                                            wire:model="turnstileResponse"
                                            data-theme="light"
                                            data-size="flexible"
                                            data-language="{{ app()->getLocale() }}"
                                        />
                                    </div>
                                    @error('turnstileResponse')
                                    <div class="error-message">
                                        {{ $message }}
                                    </div>
                                    @enderror
                                </div>
                            @endif
                            <div class="button-set">
                                <button type="submit" class="label1 gld-dashed-button">{{ __('general.button.back') }}</button>
                                <button type="submit" class="label1 arrow-right-button gld-button">{{ __('general.button.continue') }}</button>
                            </div>
                        </div>
                    @endif
                </article>
            </form>
        </div>
    </div>
</section>
@script
<script>
    document.getElementById('important-notices-form').addEventListener('submit', function (event) {
        event.preventDefault();
        if (document.getElementById('accept-notice').checked) {
            var appEnv = "{{ $appenv }}";
            if (appEnv === 'local') {
                //Start--Local CAPTCHA bypass--
                fetch('/create-anonymous-session-local', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ token: "tokenlocal" })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.href = '{{ route("search-gazette", ["locale" => app()->getLocale()]) }}';
                        } else {
                            alert('Failed to proceed. Please refresh the page and try again.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred. Please refresh the page and try again.');
                    });
                //End--Local CAPTCHA bypass--

            } else if (appEnv === 'production') {
                const turnstileResponse = turnstile.getResponse();
                if (turnstileResponse) {
                    // Perform AJAX request to create an anonymous session
                    fetch('/create-anonymous-session', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({ token: turnstileResponse })
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Redirect to the desired page
                                window.location.href = '{{ route("search-gazette", ["locale" => app()->getLocale()]) }}';
                            } else {
                                alert('Failed to proceed. Please refresh the page and try again.');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred. Please refresh the page and try again.');
                        });
                } else {
                    alert('Please complete the CAPTCHA.');
                }
            }
        } else {
            alert('Please accept the notice.');
        }
    });
</script>
@endscript
