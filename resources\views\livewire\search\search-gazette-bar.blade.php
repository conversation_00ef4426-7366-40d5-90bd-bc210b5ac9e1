<form class="search-gazette-bar-wrapper" wire:submit.prevent="submitSearchKeyword(); document.documentElement.style.overflow = 'hidden'; document.body.style.cursor = 'wait'; document.body.style.pointEvent = 'none'; ">
    <section class="search-gazette-bar-container">
        <label aria-label="{{ __('general.search_gazette.input_placeholder') }}" class="search-gazette-bar-label">
            <div class="gld-svg-container search-icon-container">
                <span class="search-icon svg"></span>
            </div>
            <input placeholder="{{ __('general.search_gazette.input_placeholder') }}" name="search_keyword" class="label2 {{ isset($searchKeyword) && !empty($searchKeyword) ? 'show-clear' : '' }}" wire:model.live="searchKeyword" />
            @if(isset($searchKeyword) && !empty($searchKeyword))
                <button type="button" class="gld-svg-container clear-icon-container" title="Clear" aria-label="Clear" wire:click="$set('searchKeyword', '')">
                    <span class="clear-input-icon svg"></span>
                </button>
            @endif
        </label>
        <button type="submit" class="gld-button label1">
            {{ __('general.search_gazette.search') }}
        </button>
    </section>
    @if (isset($keywords) && !empty($keywords))
        <section class="search-gazette-bar-history-container no-print">
            <div class="search-gazette-bar-history-header">
                <div class="search-gazette-bar-history-title">
                    <div class="gld-svg-container">
                        <span class="schedule-icon svg"></span>
                    </div>
                    <h2 class="label1">{{ __('general.search_gazette.recent_search') }}</h2>
                </div>
                <button type="button" class="gld-dashed-button label1" wire:click="clearSearchCookie">
                    {{ __('general.search_gazette.clear_history') }}
                </button>
            </div>

            <ul class="search-gazette-bar-history-list">
                @foreach($keywords as $keyword)
                    <li>
                        <button type="button" class="label2" wire:click="setSearchKeyword('{{ $keyword }}'); document.documentElement.style.overflow = 'hidden'; document.body.style.cursor = 'wait'; document.body.style.pointEvent = 'none'; ">
                            {{$keyword}}
                        </button>
                    </li>
                @endforeach
            </ul>
        </section>
    @endif
</form>
