# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.


## Main toolbar buttons (tooltips and alt text for images)

pdfjs-previous-button =
    .title = ຫນ້າກ່ອນຫນ້າ
pdfjs-previous-button-label = ກ່ອນຫນ້າ
pdfjs-next-button =
    .title = ຫນ້າຖັດໄປ
pdfjs-next-button-label = ຖັດໄປ
# .title: Tooltip for the pageNumber input.
pdfjs-page-input =
    .title = ຫນ້າ
# Variables:
#   $pagesCount (Number) - the total number of pages in the document
# This string follows an input field with the number of the page currently displayed.
pdfjs-of-pages = ຈາກ { $pagesCount }
# Variables:
#   $pageNumber (Number) - the currently visible page
#   $pagesCount (Number) - the total number of pages in the document
pdfjs-page-of-pages = ({ $pageNumber } ຈາກ { $pagesCount })
pdfjs-zoom-out-button =
    .title = ຂະຫຍາຍອອກ
pdfjs-zoom-out-button-label = ຂະຫຍາຍອອກ
pdfjs-zoom-in-button =
    .title = ຂະຫຍາຍເຂົ້າ
pdfjs-zoom-in-button-label = ຂະຫຍາຍເຂົ້າ
pdfjs-zoom-select =
    .title = ຂະຫຍາຍ
pdfjs-presentation-mode-button =
    .title = ສັບປ່ຽນເປັນໂຫມດການນຳສະເຫນີ
pdfjs-presentation-mode-button-label = ໂຫມດການນຳສະເຫນີ
pdfjs-open-file-button =
    .title = ເປີດໄຟລ໌
pdfjs-open-file-button-label = ເປີດ
pdfjs-print-button =
    .title = ພິມ
pdfjs-print-button-label = ພິມ
pdfjs-save-button =
    .title = ບັນທຶກ
pdfjs-save-button-label = ບັນທຶກ
pdfjs-bookmark-button =
    .title = ໜ້າປັດຈຸບັນ (ເບິ່ງ URL ຈາກໜ້າປັດຈຸບັນ)
pdfjs-bookmark-button-label = ຫນ້າ​ປັດ​ຈຸ​ບັນ

##  Secondary toolbar and context menu

pdfjs-tools-button =
    .title = ເຄື່ອງມື
pdfjs-tools-button-label = ເຄື່ອງມື
pdfjs-first-page-button =
    .title = ໄປທີ່ຫນ້າທຳອິດ
pdfjs-first-page-button-label = ໄປທີ່ຫນ້າທຳອິດ
pdfjs-last-page-button =
    .title = ໄປທີ່ຫນ້າສຸດທ້າຍ
pdfjs-last-page-button-label = ໄປທີ່ຫນ້າສຸດທ້າຍ
pdfjs-page-rotate-cw-button =
    .title = ຫມູນຕາມເຂັມໂມງ
pdfjs-page-rotate-cw-button-label = ຫມູນຕາມເຂັມໂມງ
pdfjs-page-rotate-ccw-button =
    .title = ຫມູນທວນເຂັມໂມງ
pdfjs-page-rotate-ccw-button-label = ຫມູນທວນເຂັມໂມງ
pdfjs-cursor-text-select-tool-button =
    .title = ເປີດໃຊ້ເຄື່ອງມືການເລືອກຂໍ້ຄວາມ
pdfjs-cursor-text-select-tool-button-label = ເຄື່ອງມືເລືອກຂໍ້ຄວາມ
pdfjs-cursor-hand-tool-button =
    .title = ເປີດໃຊ້ເຄື່ອງມືມື
pdfjs-cursor-hand-tool-button-label = ເຄື່ອງມືມື
pdfjs-scroll-page-button =
    .title = ໃຊ້ການເລື່ອນໜ້າ
pdfjs-scroll-page-button-label = ເລື່ອນໜ້າ
pdfjs-scroll-vertical-button =
    .title = ໃຊ້ການເລື່ອນແນວຕັ້ງ
pdfjs-scroll-vertical-button-label = ເລື່ອນແນວຕັ້ງ
pdfjs-scroll-horizontal-button =
    .title = ໃຊ້ການເລື່ອນແນວນອນ
pdfjs-scroll-horizontal-button-label = ເລື່ອນແນວນອນ
pdfjs-scroll-wrapped-button =
    .title = ໃຊ້ Wrapped Scrolling
pdfjs-scroll-wrapped-button-label = Wrapped Scrolling
pdfjs-spread-none-button =
    .title = ບໍ່ຕ້ອງຮ່ວມການແຜ່ກະຈາຍຫນ້າ
pdfjs-spread-none-button-label = ບໍ່ມີການແຜ່ກະຈາຍ
pdfjs-spread-odd-button =
    .title = ເຂົ້າຮ່ວມການແຜ່ກະຈາຍຫນ້າເລີ່ມຕົ້ນດ້ວຍຫນ້າເລກຄີກ
pdfjs-spread-odd-button-label = ການແຜ່ກະຈາຍຄີກ
pdfjs-spread-even-button =
    .title = ເຂົ້າຮ່ວມການແຜ່ກະຈາຍຂອງຫນ້າເລີ່ມຕົ້ນດ້ວຍຫນ້າເລກຄູ່
pdfjs-spread-even-button-label = ການແຜ່ກະຈາຍຄູ່

## Document properties dialog

pdfjs-document-properties-button =
    .title = ຄຸນສົມບັດເອກະສານ...
pdfjs-document-properties-button-label = ຄຸນສົມບັດເອກະສານ...
pdfjs-document-properties-file-name = ຊື່ໄຟລ໌:
pdfjs-document-properties-file-size = ຂະຫນາດໄຟລ໌:
# Variables:
#   $size_kb (Number) - the PDF file size in kilobytes
#   $size_b (Number) - the PDF file size in bytes
pdfjs-document-properties-kb = { $size_kb } KB ({ $size_b }  ໄບຕ໌)
# Variables:
#   $size_mb (Number) - the PDF file size in megabytes
#   $size_b (Number) - the PDF file size in bytes
pdfjs-document-properties-mb = { $size_mb } MB ({ $size_b } ໄບຕ໌)
pdfjs-document-properties-title = ຫົວຂໍ້:
pdfjs-document-properties-author = ຜູ້ຂຽນ:
pdfjs-document-properties-subject = ຫົວຂໍ້:
pdfjs-document-properties-keywords = ຄໍາທີ່ຕ້ອງການຄົ້ນຫາ:
pdfjs-document-properties-creation-date = ວັນທີສ້າງ:
pdfjs-document-properties-modification-date = ວັນທີແກ້ໄຂ:
# Variables:
#   $date (Date) - the creation/modification date of the PDF file
#   $time (Time) - the creation/modification time of the PDF file
pdfjs-document-properties-date-string = { $date }, { $time }
pdfjs-document-properties-creator = ຜູ້ສ້າງ:
pdfjs-document-properties-producer = ຜູ້ຜະລິດ PDF:
pdfjs-document-properties-version = ເວີຊັ່ນ PDF:
pdfjs-document-properties-page-count = ຈຳນວນໜ້າ:
pdfjs-document-properties-page-size = ຂະໜາດໜ້າ:
pdfjs-document-properties-page-size-unit-inches = in
pdfjs-document-properties-page-size-unit-millimeters = mm
pdfjs-document-properties-page-size-orientation-portrait = ລວງຕັ້ງ
pdfjs-document-properties-page-size-orientation-landscape = ລວງນອນ
pdfjs-document-properties-page-size-name-a-three = A3
pdfjs-document-properties-page-size-name-a-four = A4
pdfjs-document-properties-page-size-name-letter = ຈົດໝາຍ
pdfjs-document-properties-page-size-name-legal = ຂໍ້ກົດຫມາຍ

## Variables:
##   $width (Number) - the width of the (current) page
##   $height (Number) - the height of the (current) page
##   $unit (String) - the unit of measurement of the (current) page
##   $name (String) - the name of the (current) page
##   $orientation (String) - the orientation of the (current) page

pdfjs-document-properties-page-size-dimension-string = { $width } × { $height } { $unit } ({ $orientation })
pdfjs-document-properties-page-size-dimension-name-string = { $width } × { $height } { $unit } ({ $name }, { $orientation })

##

# The linearization status of the document; usually called "Fast Web View" in
# English locales of Adobe software.
pdfjs-document-properties-linearized = ມຸມມອງເວັບທີ່ໄວ:
pdfjs-document-properties-linearized-yes = ແມ່ນ
pdfjs-document-properties-linearized-no = ບໍ່
pdfjs-document-properties-close-button = ປິດ

## Print

pdfjs-print-progress-message = ກຳລັງກະກຽມເອກະສານສຳລັບການພິມ...
# Variables:
#   $progress (Number) - percent value
pdfjs-print-progress-percent = { $progress }%
pdfjs-print-progress-close-button = ຍົກເລີກ
pdfjs-printing-not-supported = ຄຳເຕືອນ: ບຼາວເຊີນີ້ບໍ່ຮອງຮັບການພິມຢ່າງເຕັມທີ່.
pdfjs-printing-not-ready = ຄໍາ​ເຕືອນ​: PDF ບໍ່​ໄດ້​ຖືກ​ໂຫຼດ​ຢ່າງ​ເຕັມ​ທີ່​ສໍາ​ລັບ​ການ​ພິມ​.

## Tooltips and alt text for side panel toolbar buttons

pdfjs-toggle-sidebar-button =
    .title = ເປີດ/ປິດແຖບຂ້າງ
pdfjs-toggle-sidebar-notification-button =
    .title = ສະຫຼັບແຖບດ້ານຂ້າງ (ເອກະສານປະກອບມີໂຄງຮ່າງ/ໄຟລ໌ແນບ/ຊັ້ນຂໍ້ມູນ)
pdfjs-toggle-sidebar-button-label = ເປີດ/ປິດແຖບຂ້າງ
pdfjs-document-outline-button =
    .title = ສະ​ແດງ​ໂຄງ​ຮ່າງ​ເອ​ກະ​ສານ (ກົດ​ສອງ​ຄັ້ງ​ເພື່ອ​ຂະ​ຫຍາຍ / ຫຍໍ້​ລາຍ​ການ​ທັງ​ຫມົດ​)
pdfjs-document-outline-button-label = ເຄົ້າຮ່າງເອກະສານ
pdfjs-attachments-button =
    .title = ສະແດງໄຟລ໌ແນບ
pdfjs-attachments-button-label = ໄຟລ໌ແນບ
pdfjs-layers-button =
    .title = ສະແດງຊັ້ນຂໍ້ມູນ (ຄລິກສອງເທື່ອເພື່ອຣີເຊັດຊັ້ນຂໍ້ມູນທັງໝົດໃຫ້ເປັນສະຖານະເລີ່ມຕົ້ນ)
pdfjs-layers-button-label = ຊັ້ນ
pdfjs-thumbs-button =
    .title = ສະແດງຮູບຫຍໍ້
pdfjs-thumbs-button-label = ຮູບຕົວຢ່າງ
pdfjs-current-outline-item-button =
    .title = ຊອກຫາລາຍການໂຄງຮ່າງປະຈຸບັນ
pdfjs-current-outline-item-button-label = ລາຍການໂຄງຮ່າງປະຈຸບັນ
pdfjs-findbar-button =
    .title = ຊອກຫາໃນເອກະສານ
pdfjs-findbar-button-label = ຄົ້ນຫາ
pdfjs-additional-layers = ຊັ້ນຂໍ້ມູນເພີ່ມເຕີມ

## Thumbnails panel item (tooltip and alt text for images)

# Variables:
#   $page (Number) - the page number
pdfjs-thumb-page-title =
    .title = ໜ້າ { $page }
# Variables:
#   $page (Number) - the page number
pdfjs-thumb-page-canvas =
    .aria-label = ຮູບຕົວຢ່າງຂອງໜ້າ { $page }

## Find panel button title and messages

pdfjs-find-input =
    .title = ຄົ້ນຫາ
    .placeholder = ຊອກຫາໃນເອກະສານ...
pdfjs-find-previous-button =
    .title = ຊອກຫາການປະກົດຕົວທີ່ຜ່ານມາຂອງປະໂຫຍກ
pdfjs-find-previous-button-label = ກ່ອນຫນ້ານີ້
pdfjs-find-next-button =
    .title = ຊອກຫາຕຳແຫນ່ງຖັດໄປຂອງວະລີ
pdfjs-find-next-button-label = ຕໍ່ໄປ
pdfjs-find-highlight-checkbox = ໄຮໄລທ໌ທັງຫມົດ
pdfjs-find-match-case-checkbox-label = ກໍລະນີທີ່ກົງກັນ
pdfjs-find-match-diacritics-checkbox-label = ເຄື່ອງໝາຍກຳກັບການອອກສຽງກົງກັນ
pdfjs-find-entire-word-checkbox-label = ກົງກັນທຸກຄຳ
pdfjs-find-reached-top = ມາຮອດເທິງຂອງເອກະສານ, ສືບຕໍ່ຈາກລຸ່ມ
pdfjs-find-reached-bottom = ຮອດຕອນທ້າຍຂອງເອກະສານ, ສືບຕໍ່ຈາກເທິງ
pdfjs-find-not-found = ບໍ່ພົບວະລີທີ່ຕ້ອງການ

## Predefined zoom values

pdfjs-page-scale-width = ຄວາມກວ້າງໜ້າ
pdfjs-page-scale-fit = ໜ້າພໍດີ
pdfjs-page-scale-auto = ຊູມອັດຕະໂນມັດ
pdfjs-page-scale-actual = ຂະໜາດຕົວຈິງ
# Variables:
#   $scale (Number) - percent value for page scale
pdfjs-page-scale-percent = { $scale }%

## PDF page

# Variables:
#   $page (Number) - the page number
pdfjs-page-landmark =
    .aria-label = ໜ້າ { $page }

## Loading indicator messages

pdfjs-loading-error = ມີຂໍ້ຜິດພາດເກີດຂື້ນຂະນະທີ່ກຳລັງໂຫລດ PDF.
pdfjs-invalid-file-error = ໄຟລ໌ PDF ບໍ່ຖືກຕ້ອງຫລືເສຍຫາຍ.
pdfjs-missing-file-error = ບໍ່ມີໄຟລ໌ PDF.
pdfjs-unexpected-response-error = ການຕອບສະໜອງຂອງເຊີບເວີທີ່ບໍ່ຄາດຄິດ.
pdfjs-rendering-error = ມີຂໍ້ຜິດພາດເກີດຂື້ນຂະນະທີ່ກຳລັງເຣັນເດີຫນ້າ.

## Annotations

# Variables:
#   $date (Date) - the modification date of the annotation
#   $time (Time) - the modification time of the annotation
pdfjs-annotation-date-string = { $date }, { $time }
# .alt: This is used as a tooltip.
# Variables:
#   $type (String) - an annotation type from a list defined in the PDF spec
# (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
pdfjs-text-annotation-type =
    .alt = [{ $type } ຄຳບັນຍາຍ]

## Password

pdfjs-password-label = ໃສ່ລະຫັດຜ່ານເພື່ອເປີດໄຟລ໌ PDF ນີ້.
pdfjs-password-invalid = ລະຫັດຜ່ານບໍ່ຖືກຕ້ອງ. ກະລຸນາລອງອີກຄັ້ງ.
pdfjs-password-ok-button = ຕົກລົງ
pdfjs-password-cancel-button = ຍົກເລີກ
pdfjs-web-fonts-disabled = ຟອນເວັບຖືກປິດໃຊ້ງານ: ບໍ່ສາມາດໃຊ້ຟອນ PDF ທີ່ຝັງໄວ້ໄດ້.

## Editing

pdfjs-editor-free-text-button =
    .title = ຂໍ້ຄວາມ
pdfjs-editor-free-text-button-label = ຂໍ້ຄວາມ
pdfjs-editor-ink-button =
    .title = ແຕ້ມ
pdfjs-editor-ink-button-label = ແຕ້ມ

## Remove button for the various kind of editor.


##

# Editor Parameters
pdfjs-editor-free-text-color-input = ສີ
pdfjs-editor-free-text-size-input = ຂະຫນາດ
pdfjs-editor-ink-color-input = ສີ
pdfjs-editor-ink-thickness-input = ຄວາມຫນາ
pdfjs-editor-ink-opacity-input = ຄວາມໂປ່ງໃສ
pdfjs-free-text =
    .aria-label = ຕົວແກ້ໄຂຂໍ້ຄວາມ
pdfjs-free-text-default-content = ເລີ່ມພິມ...
pdfjs-ink =
    .aria-label = ຕົວແກ້ໄຂຮູບແຕ້ມ
pdfjs-ink-canvas =
    .aria-label = ຮູບພາບທີ່ຜູ້ໃຊ້ສ້າງ

## Alt-text dialog


## Editor resizers
## This is used in an aria label to help to understand the role of the resizer.


## Color picker


## Show all highlights
## This is a toggle button to show/hide all the highlights.


## New alt-text dialog
## Group note for entire feature: Alternative text (alt text) helps when people can't see the image. This feature includes a tool to create alt text automatically using an AI model that works locally on the user's device to preserve privacy.


## Image alt-text settings


## "Annotations removed" bar


## Add a signature dialog


## Tab names


## Tab panels


## Controls


## Dialog buttons


## Main menu for adding/removing signatures


## Editor toolbar


## Edit signature description dialog

