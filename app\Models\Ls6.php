<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ls6 extends Model
{
    use SoftDeletes;

    protected $table = 'ls6';
    protected $primaryKey = 'id';

    public function department()
    {
        return $this->belongsTo(Department::class, 'dept_id');
    }

    public function typeDetail()
    {
        return $this->belongsTo(Type::class, 'type');
    }
}
