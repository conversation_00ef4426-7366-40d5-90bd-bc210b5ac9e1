/*---------*/
/* footer */
/*---------*/
footer {
    background: var(--grey-white);
    border-top: 1px solid var(--grey-200);
}

.gld-footer-container {
    display: flex;
    flex-direction: column;
    padding: var(--spacing-big);
    justify-content: center;
    align-items: center;
    text-align: center;
    gap: var(--spacing-big);
}

.gld-footer-container .gld-footer-copyright {
    color: var(--shades-700);
}

.gld-footer-left {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: var(--spacing-xx-sm);
}


.gld-footer-links ul {
    display: flex;
    height: 36px;
    width: 100%;
    justify-content: center;
    align-items: center;
}

.gld-footer-links ul li:not(:last-child) a::after {
    content: "";
    width: 1px;
    height: inherit;
    border-left: 1px dashed var(--grey-400);
    margin: 0 var(--spacing-md);
}

.gld-footer-links a {
    color: var(--grey-900);
    text-decoration: none;
    padding: var(--spacing-sm) 0;
}

.gld-footer-links a:hover {
    text-decoration: underline;
}

.gld-footer-right ul {
    display: flex;
    align-items: center;
    gap: var(--spacing-x-big);
}

#w3c-footer-img.gld-img-container {
    max-width: 78px;
    width: auto;
    max-height: 28px;
    height: auto;
}

#web-for-all-footer-img.gld-img-container {
    max-width: 94px;
    width: auto;
    max-height: 48px;
    height: auto;
}

#brand-hk-img.gld-img-container {
    max-width: 103px;
    width: auto;
    max-height: 36px;
    height: auto;
}


@media (min-width: 992px) {
    .gld-footer-container {
        justify-content: start;
        text-align: start;
        flex-direction: row;
        max-width: 1440px;
        width: 100%;
        padding: 36px var(--spacing-container);
        margin: auto;
    }

    .gld-footer-left {
        gap: var(--spacing-sm);
    }

    .gld-footer-links ul {
        justify-content: start;
    }

    #w3c-footer-img.gld-img-container {
        width: 102px;
        height: 36px;
    }

    #web-for-all-footer-img.gld-img-container {
        width: 124px;
        height: 64px;
    }

    #brand-hk-img.gld-img-container {
        width: 136px;
        height: 48px;
    }
}

