<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\App;
use App\Http\Controllers\CreateAnonymousSessionController;
use App\Http\Controllers\PdfController;

Route::middleware([\App\Http\Middleware\GetLocale::class])->group(function () {
  
    Route::get('/viewer', [PdfController::class, 'view'])->name('pdf.viewer');
    Route::get('/pdf', [PdfController::class, 'view'])->name('pdf.show');
    // Route::get('/pdf', [PdfController::class, 'show'])->name('pdf.show');

    Route::get('/{locale?}', function () {
        $pageTitle = '';
        return view('parent.homepage-parent', compact('pageTitle'));
    })->name('home');

    Route::get('{locale?}/maintenance-notice', function () {
        return view('parent.maintenance-parent');
    })->name('maintenance-notice');

    Route::get('{locale?}/contact-us', function () {
        $pageTitle = 'Contact Us';
        return view('parent.contact-us-page-parent', compact('pageTitle'));
    })->name('contact-us');

    Route::get('{locale?}/text-size', function () {
        $pageTitle = 'Text Size';
        return view('parent.text-size-page-parent', compact('pageTitle'));
    })->name('text-size');

    Route::get('{locale?}/important-notices', function () {
        $pageTitle = 'Important Notices';
        return view('parent.important-notices-page-parent', compact('pageTitle'));
    })->name('important-notices');
    // Route::get('important-notices/{showCaptcha?}', \App\Livewire\ImportantNoticesPage::class)->name('important-notices');

    Route::get('{locale?}/whats-new', function () {
        $pageTitle = "What’s New";
        return view('parent.whats-new-page-parent', compact('pageTitle'));
    })->name('whats-new');

    Route::get('{locale?}/search-gazette', function () {
        $pageTitle = "Search Gazette Notice";
        return view('parent.search-gazette-page-parent', compact('pageTitle'));
    })->name('search-gazette');
    // Route::get('search-gazette', \App\Livewire\SearchGazettePageParent::class)->name('search-gazette')->defaults('pageTitle', 'Search Gazette Notice');

    Route::get('{locale?}/search-gazette/gazette', function () {
        $pageTitle = "Gazette";
        return view('parent.search-gazette-result-parent', compact('pageTitle'));
    })->name('search-gazette-result');

    Route::get('{locale?}/list-of-gazette', function () {
        $pageTitle = "List of Gazette";
        return view('parent.list-of-gazette-page-parent', compact('pageTitle'));
    })->name('list-of-gazette');

    Route::get('{locale?}/list-of-gazette/result/{id}/{category}', function ($id) {
        $pageTitle = "List of Gazette Result";
        return view('parent.list-of-gazette-result-parent', compact('pageTitle'));
    })->name('list-of-gazette-result');

    Route::post('/create-anonymous-session', [CreateAnonymousSessionController::class, 'createAnonymousSession'])->name('create-anonymous-session');
    
    Route::post('/create-anonymous-session-local', [CreateAnonymousSessionController::class, 'createAnonymousSessionLocal'])->name('create-anonymous-session-local');
});

Route::get('{locale?}/language', function ($lang) {
    if (!in_array($lang, ['en', 'zh'])) {
        $lang = 'en';
    }
    session()->put('locale', $lang);

    $previousUrl = url()->previous();
    $parsedUrl = parse_url($previousUrl);
    $path = $parsedUrl['path'] ?? '/';
    $query = $parsedUrl['query'] ?? '';

    $pathSegments = explode('/', trim($path, '/'));

    if (in_array($pathSegments[0], ['en', 'zh'])) {
        array_shift($pathSegments);
    }

    array_unshift($pathSegments, $lang);
    $newPath = implode('/', $pathSegments);

    $newUrl = url($newPath . ($query ? '?' . $query : ''));
    return redirect($newUrl);
})->name("change.lang");

Route::fallback(function () {
    return redirect()->route('home');
});

//Route::middleware('auth')->group(function () {
//  Route::get('/', function () {
//    // return view('welcome');
//    return Redirect::route('dashboard');
//  });
//
//  Route::middleware([
//    'auth:sanctum',
//    config('jetstream.auth_session'),
//    'verified',
//  ])->group(function () {
//      Route::get('/dashboard', function () {
//          return view('dashboard');
//      })->name('dashboard');
//  });
//});
