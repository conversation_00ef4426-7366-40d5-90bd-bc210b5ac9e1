.search-gazette-bar-wrapper {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.search-gazette-bar-container {
    display: flex;
    padding: var(--spacing-xx-sm);
    background: var(--primary-400);
    gap: var(--spacing-xx-sm);
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-lg);
}

.search-gazette-bar-container button {
    height: 40px;
}

.search-gazette-bar-label {
    flex-grow: 1;
    display: flex;
    align-items: center;
    position: relative;
    gap: var(--spacing-xx-sm);
}

.search-gazette-bar-label .gld-svg-container {
    position: absolute;
    width: 14px;
    height: 14px;
    border: none;
    background: none;
}

.search-gazette-bar-label .search-icon-container {
    color: var(--grey-900);
    left: var(--spacing-sm);
}

.search-gazette-bar-label .clear-icon-container {
    color: var(--grey-600);
    right: var(--spacing-sm);
}

.search-gazette-bar-label .clear-icon-container:hover {
    color: var(--grey-900);
}

.search-gazette-bar-label input {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    border: 1px solid var(--grey-200);
    color: var(--grey-900);
    padding: var(--spacing-sm);
    padding-left: calc(var(--spacing-sm) + var(--spacing-sm) + var(--spacing-x-sm));
}

.search-gazette-bar-label input:focus {
    outline: none;
    border: 2px solid var(--primary-600);
}

.search-gazette-bar-label input::placeholder {
    color: var(--grey-600);
}

.search-gazette-bar-label input:placeholder-shown {
    text-overflow: ellipsis;
}

.search-gazette-bar-label .show-clear {
    padding-right: calc(var(--spacing-sm) + var(--spacing-sm) + var(--spacing-x-sm));
}

.search-gazette-bar-history-header {
    display: flex;
    justify-content: space-between;
}

.search-gazette-bar-history-header .search-gazette-bar-history-title {
    display: flex;
    gap: var(--spacing-x-sm);
    align-items: center;
}

.search-gazette-bar-history-header .search-gazette-bar-history-title .gld-svg-container {
    width: 20px;
    height: 20px;
}

.search-gazette-bar-history-header button {
    height: 32px;
}

.search-gazette-bar-history-list {
    display: flex;
    text-wrap: nowrap;
    overflow-x: auto;
    gap: var(--spacing-x-sm);
    margin-top: var(--spacing-sm);
}

.search-gazette-bar-history-list button {
    display: inline-block;
    color: var(--grey-600);
    background: var(--grey-white);
    border-radius: 6px;
    border: 1px solid var(--grey-200);
    padding: var(--spacing-x-sm) var(--spacing-sm);
    max-width: 240px;
    text-overflow: ellipsis;
    overflow: hidden;
    transition: all 0.2s ease;
}

.search-gazette-bar-history-list button:hover {
    border: 1px solid var(--grey-400);
    transition: all 0.2s ease;
}

@media (min-width: 768px) {
    .search-gazette-bar-container {
        padding: var(--spacing-x-sm);
        gap: var(--spacing-x-sm);
    }

    .search-gazette-bar-container button {
        height: 56px;
    }

    .search-gazette-bar-history-header button {
        height: 40px;
    }

    .search-gazette-bar-wrapper {
        gap: var(--spacing-md);
    }

    .search-gazette-bar-label .show-clear {
        padding-right: calc(var(--spacing-md) + var(--spacing-sm) + var(--spacing-sm));
    }

    .search-gazette-bar-label .gld-svg-container {
        width: 20px;
        height: 20px;
    }

    .search-gazette-bar-label .search-icon-container {
        left: var(--spacing-md);
    }

    .search-gazette-bar-label .clear-icon-container {
        right: var(--spacing-md);
    }

    .search-gazette-bar-label input {
        padding: var(--spacing-sm);
        padding-left: calc(var(--spacing-md) + var(--spacing-sm) + var(--spacing-sm));
    }
}
