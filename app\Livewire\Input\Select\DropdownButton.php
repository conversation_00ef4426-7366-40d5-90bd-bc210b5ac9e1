<?php

namespace App\Livewire\Input\Select;

use Livewire\Attributes\Reactive;
use Livewire\Component;

class DropdownButton extends Component
{
    public string $id = '';
    public string $customRender = '';
    public string $aria_label = '';
    public array $type = [];
    #[Reactive] public array $options = [];
    public string $firstOption = '';
    #[Reactive] public bool $open = true;
    #[Reactive] public $selected;
    #[Reactive] public bool $disabled = true;

    public function render()
    {
        return view('livewire.input.select.dropdown-button');
    }
}
