<?php

namespace App\Livewire\Search;

use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Illuminate\Support\Facades\DB;
use App\Models\SearchGazettePeriod as SearchGazettePeriodModel;
use App\Models\Gazette;
use Livewire\Attributes\Reactive;
use Livewire\WithPagination;
use Elastic\Elasticsearch\ClientBuilder;

class SearchGazetteResultPage extends Component
{
    public array $category = [];
    public bool $pagination = true;
    public array $column = [];
    public array $yearAndVolumeOptions = [];
    public array $dateAndIssueOptions = [];
    public array $perPageOptions = [5, 20, 50];
    public array $orderByOptions = ['1' => 'relevance', '2' => 'dateno desc', '3' => 'dateno asc', '4' => 'title asc', '5' => 'title desc'];
    public bool $isLs6 = false;

    public string $period = '';
    public string $queryStringCategory = '';
    public string $yearAndVolume = '';
    public string $dateAndIssue = '';
    public string $supplementNo = '';
    public string $group = '';
    public string $searchKeyword = '';
    public $totalPage;
    public $currentPage;
    public $fromCount;
    public $toCount;
    public $prevDisabled = false;
    public $nextDisabled = false;

    // #[Url(as: 'p', except: '')] public string $period = '';
    // #[Url(as: 'c', except: '')] public string $queryStringCategory = '';
    // #[Url(as: 'yv', except: '')] public string $yearAndVolume = '';
    // #[Url(as: 'di', except: '')] public string $dateAndIssue = '';
    // #[Url(as: 'sn', except: '')] public string $supplementNo = '';
    // #[Url(as: 'g', except: '')] public string $group = '';
    // #[Url(as: 'kw', except: '')] public string $searchKeyword = '';



    #[Url(as: 'page', except: '')] public int $page = 1;
    #[Url(as: 'per_page', except: '')] public string $perPage = '';

    #[Url(as: 'orderBy', except: '')] public int $orderBy = 1;
    #[Url(as: 'cov', except: '')] public string $queryStringColumn = '';

    public $notices = [];
    public $paginationData = [];
    public $categoriesResultCount = [];

    public $resultCount = [];

    protected $listeners = ['changePage', 'previousPage', 'nextPage', 'updateProperty', 'resetFilters'];

    public function mount()
    {
        $categoryQuery = request()->query('c', '1');
        $this->searchKeyword = request()->query('kw', '');
        $this->queryStringCategory = gettype($categoryQuery) === 'array' ? implode(',', $categoryQuery) : $categoryQuery;
        $this->category = $categoryQuery ? explode(',', $categoryQuery) : [];
        $this->period = request()->query('p', SearchGazettePeriodModel::getDisplayPeriod()[0]);
        $this->yearAndVolume = request()->query('yv', '');
        $this->dateAndIssue = request()->query('di', '');
        $this->supplementNo = request()->query('sn', '');
        $this->group = request()->query('g', '');
        $this->page = request()->query('page', 1);
        $this->perPage = request()->query('per_page', 5);
        if (!in_array($this->perPage, $this->perPageOptions)) {
            $this->perPage = $this->perPageOptions[0];
        }
        $this->orderBy = request()->query('orderBy', 1);
        if (!in_array($this->orderBy, array_keys($this->orderByOptions))) {
            $this->orderBy = 1;
        }

        $columnQuery = request()->query('cov', '1,2');
        $this->queryStringColumn = gettype($columnQuery) === 'array' ? implode(',', $columnQuery) : $columnQuery;
        $this->column = $columnQuery ? explode(',', $columnQuery) : [];

        $this->yearAndVolumeOptions = $this->getYearAndVolumeOptions($this->period);
        $this->dateAndIssueOptions = $this->getDateAndIssueOptions($this->yearAndVolume);

        $this->isLs6 = in_array('4', $this->category);

        $this->paginationData = [
            'current_page' => 1,
            'last_page' => 1,
            'per_page' => $this->perPage,
            'total' => 0,
        ];

        // Only load notices if a keyword is present
        if (!empty($this->searchKeyword)) {
            \Log::info('SearchKeyword in mount(): ' . $this->searchKeyword);
            $this->loadNotices();

            $this->totalPage = ceil($this->paginationData['total'] / $this->paginationData['per_page']);
            $this->currentPage = $this->page;

            $this->fromCount = ($this->paginationData['current_page'] - 1) * $this->paginationData['per_page'] + 1;
            $this->toCount = $this->paginationData['current_page'] * $this->paginationData['per_page'];
            if ($this->toCount > $this->paginationData['total']) {
                $this->toCount = $this->paginationData['total'];
            }
        }
    }

    public function loadNotices()
    {
        set_time_limit(0);
        \Log::info('SearchKeyword in loadNotices(): ' . $this->searchKeyword);
        $this->paginationData = [
            'current_page' => 1,
            'last_page' => 1,
            'per_page' => $this->perPage,
            'total' => 0,
        ];

        $categories = $this->convertCategoryToSupplementNo($this->category, $this->supplementNo);

        if (count($categories) > 0) {
            $queries = $this->generateQuery();

            if (!empty($queries)) {
                $query = array_shift($queries);
                foreach ($queries as $q) {
                    $query->union($q);
                }

                // calculate categoriesResultCount from the union query
                $counts = DB::table(DB::raw("({$query->toSql()}) as subc"))
                    ->mergeBindings($query)
                    ->select('subc.table_name', DB::raw('COUNT(*) as count'))
                    ->groupBy('subc.table_name')
                    ->get();

                $this->resultCount = [
                    'mg' => 0,
                    'ls' => 0,
                    'ls4' => 0,
                    'ls6' => 0,
                ];

                foreach ($counts as $count) {
                    switch ($count->table_name) {
                        case 'mg':
                            $this->resultCount['mg'] = $count->count;
                            break;
                        case 'ls1':
                        case 'ls2':
                        case 'ls3':
                        case 'ls5':
                            $this->resultCount['ls'] += $count->count;
                            break;
                        case 'ls4':
                            $this->resultCount['ls4'] = $count->count;
                            break;
                        case 'ls6':
                            $this->resultCount['ls6'] = $count->count;
                            break;
                    }
                }
                $this->categoriesResultCount = $this->resultCount;

                // Wrap the union query in a subquery and apply the orderBy clause
                $finalQuery = DB::table(DB::raw("({$query->toSql()}) as sub"))
                    ->mergeBindings($query);

                $tableNames = [];
                foreach ($categories as $cat) {
                    switch ($cat) {
                        case '0':
                            $tableNames[] = 'mg';
                            break;
                        case '1':
                            $tableNames[] = 'ls1';
                            break;
                        case '2':
                            $tableNames[] = 'ls2';
                            break;
                        case '3':
                            $tableNames[] = 'ls3';
                            break;
                        case '4':
                            $tableNames[] = 'ls4';
                            break;
                        case '5':
                            $tableNames[] = 'ls5';
                            break;
                        case '6':
                            $tableNames[] = 'ls6';
                            break;
                    }
                }

                if (!empty($tableNames)) {
                    $finalQuery->whereIn('sub.table_name', $tableNames);
                }

                if ($this->orderBy == 1) {
                    if ($this->searchKeyword) {
                        // $finalQuery->orderBy('sub.score', 'desc');
                        $finalQuery->orderByRaw('ROUND(sub.score, 4) DESC');
                        $finalQuery->orderBy('sub.dateno', 'desc');

                    } else {
                        $finalQuery->orderBy('sub.dateno', 'desc');
                    }
                } else {
                    if ($this->orderBy == 2) {
                        $finalQuery->orderBy('sub.dateno', 'desc');
                    }
                    if ($this->orderBy == 3) {
                        $finalQuery->orderBy('sub.dateno', 'asc');
                    }
                    if ($this->orderBy == 4) {
                        $finalQuery->orderBy('sub.e_title', 'asc');
                    }
                    if ($this->orderBy == 5) {
                        $finalQuery->orderBy('sub.e_title', 'desc');
                    }
                }

                $bindings = $finalQuery->getBindings();
                $tableNamesBindings = $tableNames;
                if ($this->searchKeyword) {
                    array_splice($bindings, 3, count($tableNamesBindings));
                } else {
                    array_splice($bindings, 2, count($tableNamesBindings));
                }
                $bindings = array_merge($bindings, $tableNamesBindings);
                $finalQuery->bindings = $bindings;

                // \Log::info($finalQuery->toSql());
                // \Log::info($finalQuery->getBindings());

                // Paginate the results
                $paginatedNotices = $finalQuery->paginate($this->perPage, ['*'], 'page', $this->page);
                $this->notices = $paginatedNotices->items();
                $this->paginationData = [
                    'current_page' => $paginatedNotices->currentPage(),
                    'last_page' => $paginatedNotices->lastPage(),
                    'per_page' => $paginatedNotices->perPage(),
                    'total' => $paginatedNotices->total(),
                ];
            }

            $resultNotices = [];
            if (count($this->notices) > 0) {
                foreach ($this->notices as $tempNotice) {
                    $tempNotice->dayDate = \DateTime::createFromFormat('Ymd', $tempNotice->dateno)->format('d');
                    $tempNotice->monthDate = \DateTime::createFromFormat('Ymd', $tempNotice->dateno)->format('m');
                    $tempNotice->yearDate = \DateTime::createFromFormat('Ymd', $tempNotice->dateno)->format('Y');
                    $tempNotice->enDayOfWeek = \DateTime::createFromFormat('Ymd', $tempNotice->dateno)->format('l');
                    $tempNotice->zhDayOfWeek = $this->getDayOfWeek(\DateTime::createFromFormat('Ymd', $tempNotice->dateno)->format('w'));

                    $tempNotice->englishPdfUrl = "";
                    $tempNotice->chinesePdfUrl = "";

                    if ($tempNotice->table_name === 'mg') {
                        $tempNotice->englishPdfUrl = route('pdf.show', ['type' => 'egn', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => $tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                        $tempNotice->chinesePdfUrl = route('pdf.show', ['type' => 'cgn', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => $tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                    } else if ($tempNotice->table_name == 'ls1') {
                        $tempNotice->englishPdfUrl = route('pdf.show', ['type' => 'es1', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => $tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                        $tempNotice->chinesePdfUrl = route('pdf.show', ['type' => 'cs1', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => $tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                    } else if ($tempNotice->table_name == 'ls2') {
                        $tempNotice->englishPdfUrl = route('pdf.show', ['type' => 'es2', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => $tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                        $tempNotice->chinesePdfUrl = route('pdf.show', ['type' => 'cs2', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => $tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                    } else if ($tempNotice->table_name == 'ls3') {
                        $tempNotice->englishPdfUrl = route('pdf.show', ['type' => 'es3', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => $tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                        $tempNotice->chinesePdfUrl = route('pdf.show', ['type' => 'cs3', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => $tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                    } else if ($tempNotice->table_name == 'ls4') {
                        $tempNotice->englishPdfUrl = route('pdf.show', ['type' => 'ls4', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => '1', 'extra' => '1', 'nid' => $tempNotice->id, 'language' => 'en']);
                        $tempNotice->chinesePdfUrl = route('pdf.show', ['type' => 'ls4', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => '1', 'extra' => '1', 'nid' => $tempNotice->id, 'language' => 'zh']);
                    } else if ($tempNotice->table_name == 'ls5') {
                        $tempNotice->englishPdfUrl = route('pdf.show', ['type' => 'es5', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => $tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                        $tempNotice->chinesePdfUrl = route('pdf.show', ['type' => 'cs5', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'notice_no' => $tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                    } else if ($tempNotice->table_name == 'ls6') {
                        $tempNotice->englishPdfUrl = route('pdf.show', ['type' => 's6', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'group' => 'p'.$tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                        $tempNotice->chinesePdfUrl = route('pdf.show', ['type' => 's6', 'year' => $tempNotice->year, 'volume' => $tempNotice->volume, 'gno' => ($tempNotice->gno < 10) ? '0'.$tempNotice->gno : $tempNotice->gno, 'group' => 'p'.$tempNotice->notice_no, 'extra' => $tempNotice->extra]);
                    }

                    $resultNotices[] = $tempNotice;
                }
                // \Log::info($resultNotices);
                $this->notices = $resultNotices;
            }
        }
    }

    public function updated()
    {
        $this->queryStringCategory = implode(',', $this->category);
        $this->isLs6 = in_array('4', $this->category);
        $this->queryStringColumn = implode(',', $this->column);
    }

    #[On('update-property')]
    public function updateProperty($propertyName, $value): void
    {
        if ($propertyName == 'perPage') {
            if ($this->perPage != $value) {
                $this->page = 1;
                $this->paginationData['current_page'] = 1;
            }
        }

        if(is_array($this->$propertyName)) {
            if (empty($value)) {
                $this->$propertyName = [];
                return;
            }
            $this->$propertyName = explode(',', $value);
            return;
        };
        $this->$propertyName = $value;

        if ($propertyName == 'period') {
            $this->yearAndVolume = '';
            $this->yearAndVolumeOptions = $this->getYearAndVolumeOptions($value);
            $this->dateAndIssue = '';
        }

        if ($propertyName == 'yearAndVolume') {
            $this->dateAndIssue = '';
            $this->dateAndIssueOptions = $this->getDateAndIssueOptions($value);
        }

        if ($propertyName == 'perPage') {
            $this->perPage = $value;
            $this->loadNotices();
        }

        if ($propertyName == 'orderBy') {
            $this->orderBy = $value;
            $this->loadNotices();
        }

        // reset supplementNo dropdown list if Legal Notices is unchecked
        if (!in_array('2', $this->category)) {
            $this->reset(['supplementNo']);
        }

        // reset group dropdown list if LS6 is unchecked
        if (!in_array('4', $this->category)) {
            $this->reset(['group']);
        }

        $this->currentPage = $this->page;
    }

    #[On('reset-filters')]
    public function resetFilters(): void
    {
        $this->reset(['yearAndVolume', 'dateAndIssue', 'supplementNo', 'group']);
        $this->period = SearchGazettePeriodModel::getDisplayPeriod()[0];
        $this->category = ['1'];
        $this->queryStringCategory = '1';
    }

    #[On('reset-pagination')]
    public function resetPagination(): void
    {
        $this->page = 1;
        $this->paginationData['current_page'] = 1;
    }

    public function checkCategoryIsSelect(array $cate): bool
    {
        return empty(array_intersect($cate, $this->category));
    }

    #[On('previous-page')]
    public function previousPage()
    {
        $this->page = max(1, (int)$this->page - 1);
        $this->loadNotices();
        $this->prevDisabled = false;
    }

    #[On('next-page')]
    public function nextPage()
    {
        $this->page = min($this->paginationData['last_page'], (int)$this->page + 1);
        $this->loadNotices();
        $this->nextDisabled = false;
    }

    #[On('change-page')]
    public function changePage($page)
    {
        $this->page = $page;
        $this->loadNotices();
    }

    public function render()
    {
        return view('livewire.search.search-gazette-result-page', [
            'notices' => $this->notices,
            'paginationData' => $this->paginationData,
            'page' => $this->page,
            'column' => $this->column,
            'isLs6' => $this->isLs6
        ]);
    }

    private function generateQuery()
    {
        $condition = '';
        $searchVolumeGte = (Gazette::PUBLISHEDMINYEAR % 100) + 4;
        $searchVolumeLte = (date('Y') % 100) + 4;
        $isSearchInRecentYears = false;
        $searchYear = date('Y');

        // period condition
        if ($this->period && empty($this->yearAndVolume)) {
            if (strpos($this->period, '-') === false) {
                $year = substr($this->period, 0, 4);
                $condition = "g.year <= '{$year}'";
                $searchVolumeLte = ($year % 100) + 4;
                $searchYear = $year;
            } else {
                $period = explode('-', $this->period);
                $condition = "g.year <= {$period[0]} AND g.year >= {$period[1]}";
                $searchVolumeGte = ($period[1] % 100) + 4;
                $searchVolumeLte = ($period[0] % 100) + 4;
                $searchYear = $period[0];
            }
        }
        if (!empty($this->yearAndVolume)) {
            $year = substr($this->yearAndVolume, 0, 4);
            $condition = "g.year = '{$year}'";
            $searchVolumeGte = ($year % 100) + 4;
            $searchVolumeLte = ($year % 100) + 4;
            $searchYear = $year;
        }

        // date condition
        if (!empty($this->dateAndIssue)) {
            $date = substr($this->dateAndIssue, 0, 8);
            if (!empty($condition)) {
                $condition .= " AND g.dateno = '{$date}'";
            } else {
                $condition = "g.dateno = '{$date}'";
            }
        }

        // check if fall in recent years (When ES_RECENT_YEARS = 5 and current year is 2025, recent years are 2021 to 2025 )
        if (date('Y') - $searchYear < config('elasticsearch.ES_RECENT_YEARS')) {
            $isSearchInRecentYears = true;
        }

        $notices = [];
        $joinTempScores = false;
        $titleByLocale = false;
        $containsChinese = false;
        $exactSearch = false;
        $exactKeywords = [];
        $exactKeywordsGroup = [];
        $keywordLeft = '';
        $keywordLeftGroup = [];
        $multiSearch = false;
        $multiKeywords = [];
        $multiKeywordsGroup = [];
        $searchFields = $isSearchInRecentYears? ['attachment.content', 'title'] : ['title'];
        \Log::info('searchFields: ' . $searchFields[0]);

        // keyword condition
        if (!empty($this->searchKeyword)) {
            if (is_numeric($this->searchKeyword) || preg_match('/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]+$/', $this->searchKeyword)) {
                $titleByLocale = true;
            }
            $containsChinese = preg_match('/[\x{4e00}-\x{9fff}]/u', $this->searchKeyword);
            \Log::info('searchKeyword: ' . $this->searchKeyword);
            \Log::info($titleByLocale? 'titleByLocale: true' : 'titleByLocale: false');
            \Log::info($containsChinese? 'containsChinese: true' : 'containsChinese: false');

            preg_match('/"([^"]+)"\s*(.*)/', $this->searchKeyword, $quotes);
            \Log::info('count($quotes): ' . count($quotes));
            if (count($quotes) > 0) {
                preg_match_all('/"[^"\\\]*(?:\\\.[^"\\\]*)*"/', $this->searchKeyword, $matches);
                if (count($matches[0]) > 0) {
                    $keywordLeft = $this->searchKeyword;
                    foreach ($matches[0] as $keyword) {
                        $unquotedKeyword = trim($keyword, '"');
                        if (trim($unquotedKeyword) !== '') {
                            \Log::info('keywordExact: ' . $unquotedKeyword);
                            $exactKeywords[] = $unquotedKeyword;
                            $keywordLeft = implode(' ', array_diff(explode(' ', $keywordLeft), explode(' ', $keyword)));
                        }
                    }
                    \Log::info('keywordLeft: ' . $keywordLeft);
                }
            } else {
                $keywords = explode(' ', $this->searchKeyword);
                foreach ($keywords as $keyword) {
                    if (trim($keyword) !== '') {
                        \Log::info('keyword: ' . $keyword);
                        $multiKeywords[] = $keyword;
                    }
                }
            }

            if (!empty($exactKeywords)) {
                $exactSearch = true;
                $exactKeywordsGroup = $this->convertExactSearchKeywords($exactKeywords, $searchFields);
                if (!empty($keywordLeft)) {
                    $keywordLeftGroup = $this->convertExactLeftKeywords($keywordLeft, $searchFields, $isSearchInRecentYears);
                }
            } else {
                $multiKeywordsGroup = $this->convertMultiSearchKeywords($multiKeywords, $searchFields);
            }

            $matchPhraseQueries = [];
            foreach ($searchFields as $field) {
                $matchPhraseQueries[] = [
                    'match_phrase' => [
                        $field => $this->searchKeyword
                    ]
                ];
            }

            $semanticQueries = [];
            if ($isSearchInRecentYears) {
                $semanticQueries[] = [
                    'bool' => [
                        'must_not' => [
                            [
                                'bool' => [
                                    'should' => $matchPhraseQueries
                                ]
                            ],
                            ...$multiKeywordsGroup
                        ],
                        'must' => [
                            [
                                'semantic' => [
                                    'field' => 'semantic_text',
                                    'query' => $this->searchKeyword
                                ]
                            ]
                        ]
                    ]
                ];
            }

            try {
                $client = ClientBuilder::create()
                ->setHosts([config('elasticsearch.ES_HOSTS')])
                ->setBasicAuthentication(config('elasticsearch.ES_USERNAME'), config('elasticsearch.ES_PASSWORD'))
                // ->setCABundle('/var/www/public/http_ca.crt')
                ->build();

                $response = $this->searchRequest(function () use ($client, $searchVolumeGte, $searchVolumeLte, $exactSearch, $exactKeywordsGroup, $keywordLeftGroup, $multiKeywordsGroup, $searchFields, $matchPhraseQueries, $semanticQueries) {
                    $params = [
                        'index' => config('elasticsearch.ES_INDEX'),
                        'body'  => [
                            '_source' => ['filename', 'title', 'notice_type', 'notice_id', 'language'],
                            'size' => config('elasticsearch.ES_SIZE'),
                            'sort' => [
                              '_score',
                              ['dateno' => ['order' => 'desc']]
                            ],
                            'query' => [
                                'bool' => [
                                    'filter' => [
                                        [
                                            'range' => [
                                                'volume' => [
                                                    'gte' => $searchVolumeGte,
                                                    'lte' => $searchVolumeLte
                                                ]
                                            ]
                                        ]
                                    ],
                                    'should' => [
                                        [
                                            'constant_score' => [
                                                'filter' => [
                                                    'bool' => [
                                                        'should' => $matchPhraseQueries
                                                    ]
                                                ],
                                                'boost' => 100.0
                                            ]
                                        ],
                                        [
                                            'bool' => [
                                                'must_not' => [
                                                    [
                                                        'bool' => [
                                                            'should' => $matchPhraseQueries
                                                        ]
                                                    ]
                                                ],
                                                'should' => $multiKeywordsGroup
                                            ]
                                        ],
                                        ...$semanticQueries
                                    ]
                                ]
                            ],
                            'highlight' => [
                                'max_analyzed_offset' => 2000000,
                                'order' => 'score',
                                'highlight_query' => [
                                    'bool' => [
                                        'should' => [
                                            [
                                                'bool' => [
                                                    'should' => $matchPhraseQueries
                                                ]
                                            ],
                                            [
                                                'bool' => [
                                                    'must_not' => [
                                                        [
                                                            'bool' => [
                                                                'should' => $matchPhraseQueries
                                                            ]
                                                        ]
                                                    ],
                                                    'should' => $multiKeywordsGroup
                                                ]
                                            ]
                                        ]
                                    ]
                                ],
                                'fields' => [
                                    'attachment.content' => [
                                        'fragment_size' => 100,
                                        'number_of_fragments' => 1
                                    ],
                                    'title' => [
                                        'fragment_size' => 0,
                                        'number_of_fragments' => 1
                                    ]
                                ],
                                'pre_tags' => ["<span class='search-highlight'>"],
                                'post_tags' => ["</span>"]
                            ]
                        ]
                    ];

                    $exactSearchParams = [
                        'index' => config('elasticsearch.ES_INDEX'),
                        'body'  => [
                            '_source' => ['filename', 'title', 'notice_type', 'notice_id', 'language'],
                            'size' => config('elasticsearch.ES_SIZE'),
                            'sort' => [
                              '_score',
                              ['dateno' => ['order' => 'desc']]
                            ],
                            'query' => [
                                'bool' => [
                                    'filter' => [
                                        [
                                            'range' => [
                                                'volume' => [
                                                    'gte' => $searchVolumeGte,
                                                    'lte' => $searchVolumeLte
                                                ]
                                            ]
                                        ]
                                    ],
                                    'must' => [
                                        [
                                            'constant_score' => [
                                                'filter' => [
                                                    'bool' => [
                                                        'must' => $exactKeywordsGroup
                                                    ]
                                                ],
                                                'boost' => 98.0
                                            ]
                                        ]
                                    ],
                                    'should' => [
                                        [
                                            'bool' => [
                                                'must' => [
                                                    'match_all' => new \stdClass()
                                                ]
                                            ]
                                        ],
                                        [
                                            'bool' => [
                                                'must' => $keywordLeftGroup
                                            ]
                                        ]
                                    ]
                                ]
                            ],
                            'highlight' => [
                                'max_analyzed_offset' => 2000000,
                                'order' => 'score',
                                'fields' => [
                                    'attachment.content' => [
                                        'fragment_size' => 100,
                                        'number_of_fragments' => 1
                                    ],
                                    'title' => [
                                        'fragment_size' => 0,
                                        'number_of_fragments' => 1
                                    ]
                                ],
                                'pre_tags' => ["<span class='search-highlight'>"],
                                'post_tags' => ["</span>"]
                            ]
                        ]
                    ];

                    if ($exactSearch) {
                        // \Log::info($exactSearchParams);
                        return $client->search($exactSearchParams);
                    } else {
                        // \Log::info($params);
                        return $client->search($params);
                    }
                });
                // \Log::info("================");
                // \Log::info($response);

            } catch (\Exception $e) {
                \Log::error("error connecting to elasticsearch");
                \Log::error($e->getMessage());
            }

            // \Log::channel('elasticsearch')->info($response->hits->total->value);
            // \Log::channel('elasticsearch')->info($response->hits->hits);
            // \Log::info($response->hits->total->value);
            // \Log::info($response->hits->hits);
            $scores = [];
            $resultHits = $response->hits->total->value ?? 0;

            if ($resultHits > 0) {
                foreach ($response->hits->hits as $hit) {
                    if ($hit->_score > 0) { // remove results that score <= 0
                        if ($hit->_score < 0.92 && $hit->_source->notice_type == 'ls6') {
                            continue;
                        }
                        $noticeKey = $hit->_source->notice_type . '_' . $hit->_source->notice_id;
                        // Check for duplicates
                        if (!in_array($noticeKey, $notices)) {
                            $notices[] = $noticeKey;
                            $scores[$noticeKey] = [
                                'notice_type' => $hit->_source->notice_type,
                                'notice_id' => $hit->_source->notice_id,
                                'filename' => $hit->_source->filename,
                                'score' => $hit->_score,
                                'highlight_en' => isset($hit->highlight->{'attachment.content'}) ? $hit->highlight->{'attachment.content'} : null,
                                'highlight_zh' => isset($hit->highlight->{'attachment.content'}) ? $hit->highlight->{'attachment.content'} : null,
                                'highlight_title' => isset($hit->highlight->title) ? $hit->highlight->title : null,
                                'title' => $titleByLocale ? null : $hit->_source->title,
                                'language' => $hit->_source->language
                            ];
                        } else {
                            $highlight = isset($hit->highlight->{'attachment.content'}) ? $hit->highlight->{'attachment.content'} : '';
                            if ($scores[$noticeKey]['language'] == 'en') {
                                $scores[$noticeKey]['highlight_zh'] = empty($highlight) ? null : $highlight;
                            } else if ($scores[$noticeKey]['language'] == 'zh') {
                                $scores[$noticeKey]['highlight_en'] = empty($highlight) ? null : $highlight;
                            }

                            if (!$titleByLocale) {
                                if ($containsChinese && $hit->_source->language == 'zh') {
                                    $scores[$noticeKey]['title'] = $hit->_source->title;
                                }

                                if (!$containsChinese && $hit->_source->language == 'en') {
                                    $scores[$noticeKey]['title'] = $hit->_source->title;
                                }
                            }
                        }
                    }
                }
                // \Log::info($scores);
            } else {
                $condition .= (empty($condition) ? '' : ' AND ') ." 1=0";
            }

            // Drop the temporary table if it exists
            DB::statement('DROP TEMPORARY TABLE IF EXISTS temp_scores');
            // Create a temporary table for scores
            // DB::statement('CREATE TEMPORARY TABLE temp_scores (notice_type VARCHAR(255) COLLATE utf8mb4_unicode_ci, notice_id BIGINT(20) COLLATE utf8mb4_unicode_ci, filename VARCHAR(255) COLLATE utf8mb4_unicode_ci, score FLOAT COLLATE utf8mb4_unicode_ci, highlight_en TEXT COLLATE utf8mb4_unicode_ci, highlight_zh TEXT COLLATE utf8mb4_unicode_ci, highlight_title TEXT COLLATE utf8mb4_unicode_ci, title TEXT COLLATE utf8mb4_unicode_ci)');
            DB::statement('CREATE TEMPORARY TABLE temp_scores
                (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    notice_type VARCHAR(255) COLLATE utf8mb4_unicode_ci,
                    notice_id BIGINT(20) COLLATE utf8mb4_unicode_ci,
                    filename VARCHAR(255) COLLATE utf8mb4_unicode_ci,
                    score FLOAT COLLATE utf8mb4_unicode_ci,
                    highlight_en TEXT COLLATE utf8mb4_unicode_ci,
                    highlight_zh TEXT COLLATE utf8mb4_unicode_ci,
                    highlight_title TEXT COLLATE utf8mb4_unicode_ci,
                    title TEXT COLLATE utf8mb4_unicode_ci
                )
            ');

            $categoryKeys = ['mg', 'ls', 'ls4', 'ls6'];
            $categoryMap = [
                'mg'  => 'mg',
                'ls1' => 'ls',
                'ls2' => 'ls',
                'ls3' => 'ls',
                'ls5' => 'ls',
                'ls4' => 'ls4',
                'ls6' => 'ls6',
            ];
            $categoryCounts = array_fill_keys($categoryKeys, 0);
            $categoriesFull = [];
            $maxPerCategory = config('elasticsearch.ES_RESULT_SIZE');
            $insertedNoticeIds = [];
            $resultCount = 0;

            foreach ($scores as $noticeKey => $score_data) {
                $cat = $score_data['notice_type'] ?? null;
                $group = $cat && isset($categoryMap[$cat]) ? $categoryMap[$cat] : null;
                if (!$group || !in_array($group, $categoryKeys)) {
                    continue;
                }
                if (isset($categoriesFull[$group])) {
                    continue;
                }

                DB::table('temp_scores')->insert([
                    'notice_type' => $score_data['notice_type'],
                    'notice_id' => $score_data['notice_id'],
                    'filename' => $score_data['filename'],
                    'score' => $score_data['score'],
                    'highlight_en' => is_array($score_data['highlight_en']) ? $score_data['highlight_en'][0] : $score_data['highlight_en'],
                    'highlight_zh' => is_array($score_data['highlight_zh']) ? $score_data['highlight_zh'][0] : $score_data['highlight_zh'],
                    'highlight_title' => is_array($score_data['highlight_title']) ? $score_data['highlight_title'][0] : $score_data['highlight_title'],
                    'title' => $score_data['title']
                ]);

                $insertedNoticeIds[] = $noticeKey;
                $resultCount++;
                \Log::info($resultCount . " = " . $score_data['score'] . " - ". $noticeKey . " - ". $score_data['filename'] . " - ". $score_data['title']);

                $categoryCounts[$group]++;
                if ($categoryCounts[$group] >= $maxPerCategory) {
                    $categoriesFull[$group] = true;
                    // If all categories are full, break early
                    if (count($categoriesFull) === count($categoryKeys)) {
                        break;
                    }
                }
            }

            $notices = array_values(array_filter($notices, function($notice) use ($insertedNoticeIds) {
                return in_array($notice, $insertedNoticeIds);
            }));

            $joinTempScores = true;
        }

        $tables = ['mg', 'ls1', 'ls2', 'ls3', 'ls4', 'ls5', 'ls6'];
        $queries = [];
        foreach ($tables as $table) {
            if ($table == 'ls4') {
                $query = DB::table("$table as n")
                    ->leftJoin('gazette as g', function ($join) {
                        $join->on('n.dateno', '=', 'g.dateno')
                            ->on('n.volume', '=', 'g.volume')
                            ->on('n.gno', '=', 'g.gno')
                            ->where('n.file_ind', '=', 'Y');
                    })
                    ->select('n.id', 'n.e_level_title as e_title', 'n.c_level_title as c_title', 'n.dateno', 'n.gno', 'n.volume', DB::raw("'$table' as table_name"), 'g.year', DB::raw("'' as notice_no"), 'g.extra', DB::raw("'' as e_position"), DB::raw("'' as c_position"));

                $searchCondition = (empty($condition) ? '' : $condition);

                if ($joinTempScores) {
                    $searchCondition = (empty($searchCondition) ? '' : $searchCondition . ' AND') . " (CONCAT('$table', '_', n.id) IN ('".implode("','", $notices)."'))";
                    $query->leftJoin('temp_scores as ts', function ($join) use ($table) {
                        $join->on('n.id', '=', 'ts.notice_id')
                            ->where('ts.notice_type', '=', $table);
                    })
                        ->addSelect('ts.score')
                        ->addSelect('ts.highlight_en')
                        ->addSelect('ts.highlight_zh')
                        ->addSelect('ts.highlight_title')
                        ->addSelect('ts.title');
                }

                $queries[] = $query
                    ->where('g.status', '1')
                    ->whereNull('n.deleted_at')
                    ->whereNull('g.deleted_at')
                    ->whereRaw($searchCondition);

            } else if ($table == 'ls6') {
                $query = DB::table("$table as n")
                    ->leftJoin('gazette as g', function ($join) {
                        $join->on('n.volume', '=', 'g.volume')
                            ->on('n.gno', '=', 'g.gno')
                            ->where('g.extra', '=', '0');
                    })
                    ->select('n.id', 'n.group as e_title', 'n.group as c_title', 'g.dateno', 'n.gno', 'n.volume', DB::raw("'$table' as table_name"), 'g.year', 'n.group as notice_no', 'g.extra', 'n.e_position', 'n.c_position');

                if ($this->group) {
                    $condition .= (empty($condition) ? '' : ' AND') . " n.group = " . $this->group;
                }

                $searchCondition = (empty($condition) ? '' : $condition);

                if ($joinTempScores) {
                    $searchCondition = (empty($searchCondition) ? '' : $searchCondition . ' AND') . " (CONCAT('$table', '_', n.id) IN ('".implode("','", $notices)."'))";
                    $query->leftJoin('temp_scores as ts', function ($join) use ($table) {
                        $join->on('n.id', '=', 'ts.notice_id')
                            ->where('ts.notice_type', '=', $table);
                    })
                        ->addSelect('ts.score')
                        ->addSelect('ts.highlight_en')
                        ->addSelect('ts.highlight_zh')
                        ->addSelect('ts.highlight_title')
                        ->addSelect('ts.title');
                }

                $queries[] = $query
                    ->where('g.status', '1')
                    ->whereNull('n.deleted_at')
                    ->whereNull('g.deleted_at')
                    ->whereRaw($searchCondition);

            } else {
                $query = DB::table("$table as n")
                ->leftJoin('gazette as g', 'n.gazette_id', '=', 'g.id')
                ->select('n.id', 'n.e_title', 'n.c_title', 'n.dateno', 'n.gno', 'n.volume', DB::raw("'$table' as table_name"), 'g.year', 'n.notice_no', 'g.extra', 'n.e_position', 'n.c_position');

                $searchCondition = (empty($condition) ? '' : $condition);

                if ($joinTempScores) {
                    $searchCondition = (empty($searchCondition) ? '' : $searchCondition . ' AND') . " (CONCAT('$table', '_', n.id) IN ('".implode("','", $notices)."'))";
                    $query->leftJoin('temp_scores as ts', function ($join) use ($table) {
                        $join->on('n.id', '=', 'ts.notice_id')
                            ->where('ts.notice_type', '=', $table);
                    })
                    ->addSelect('ts.score')
                    ->addSelect('ts.highlight_en')
                    ->addSelect('ts.highlight_zh')
                    ->addSelect('ts.highlight_title')
                    ->addSelect('ts.title');
                }

                $queries[] = $query
                    ->where('n.status', '1')
                    ->where('g.status', '1')
                    ->whereNull('n.deleted_at')
                    ->whereNull('g.deleted_at')
                    ->whereRaw($searchCondition);
            }
        }
        return $queries;
    }

    private function getYearAndVolumeOptions($year) {
        $result = ['' => __('general.search_gazette.pls_select')];
        if (strpos($year, '-') !== false) {
            $period = explode('-', $year);
            while ($period[0] >= $period[1]) {
                $result[$period[0]] = $period[0].' '. __('general.search_gazette.year_volume', ['volume' => ($period[0] - Gazette::MINYEAR > 100 ? $period[0] - Gazette::MINYEAR : '0' . ($period[0] - Gazette::MINYEAR))]);
                $period[0]--;
            }
        } else {
            $period = substr($year, 0, 4);
            while ($period >= Gazette::MINYEAR && $period >= Gazette::PUBLISHEDMINYEAR) {
                $result[$period] = $period.' '. __('general.search_gazette.year_volume', ['volume' => ($period - Gazette::MINYEAR > 100 ? $period - Gazette::MINYEAR : '0' . ($period - Gazette::MINYEAR))]);
                $period--;
            }
        }

        // \Log::info($result);
        return $result;
    }

    private function getDateAndIssueOptions($value) {
        $result = ['' => __('general.search_gazette.pls_select')];

        if ($value) {
            $year = substr($value, 0, 4);
            $gazettes = Gazette::where('year', $year)
                ->where('status', '1')
                ->orderBy('dateno', 'desc')
                ->get();

            foreach ($gazettes as $gazette) {
                $gazette_date = \DateTime::createFromFormat('Ymd', $gazette->dateno);
                $key = $gazette->dateno;
                if ($gazette->extra) $key = $key.'e';
                $locale = app()->getLocale();
                if ($locale == 'zh') {
                    $formatted_date = __('general.search_gazette.date_format', [
                        'day' => $gazette_date->format('d'),
                        'month' => $gazette_date->format('m'),
                        'year' => $gazette_date->format('Y')
                    ]);
                    $result[$key] = $formatted_date . ' ' . __('general.search_gazette.date_issue', ['issue_num' => $gazette->gno]) . ($gazette->extra === 1 ? ' ' . __('general.search_gazette.extraordinary') : '');
                } else {
                    $formatted_date = __('general.search_gazette.date_format', [
                        'day' => $gazette_date->format('d'),
                        'month' => $gazette_date->format('M'),
                        'year' => $gazette_date->format('Y')
                    ]);
                    $result[$key] = $formatted_date . ' ' . __('general.search_gazette.date_issue', ['issue_num' => $gazette->gno]) . ($gazette->extra === 1 ? ' ' . __('general.search_gazette.extraordinary') : '');
                }
            }

            // Force the keys into strings
            $result = array_combine(array_map('strval', array_keys($result)), $result);
        }

        return $result;
    }

    private function convertCategoryToSupplementNo($categories, $supplementNo) {
        $result = [];
        foreach ($categories as $cat) {
            switch ($cat) {
                case '1':
                    $result[] = '0';
                    break;
                case '2':
                    if ($supplementNo) {
                        $result[] = $supplementNo;
                    } else {
                        $result[] = '1';
                        $result[] = '2';
                        $result[] = '3';
                        $result[] = '5';
                    }
                    break;
                case '3':
                    $result[] = '4';
                    break;
                case '4':
                    $result[] = '6';
                    break;
            }
        }

        return $result;
    }

    public function getDayOfWeek($weekday) {
        $weekdays_text = array("星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六");
        return $weekdays_text[$weekday];
    }

    private function searchRequest(callable $callback, $maxRetries = 1) {
        $attempts = 0;
        while ($attempts <= $maxRetries) {
            try {
                return $callback();
            } catch (\Exception $e) {
                $attempts++;
                if (method_exists($e, 'getResponse') && $e->getResponse()->getStatusCode() === 408) {
                    \Log::warning("Elasticsearch request timed out (Attempt $attempts of $maxRetries)");
                } else {
                    throw $e;
                }
            }
        }
        throw new \Exception("elasticsearch request failed after $maxRetries retries: " . $e->getMessage());
    }

    private function convertExactSearchKeywords($exactKeywords, $searchFields) {
        $exactKeywordsGroup = [];     
        foreach ($exactKeywords as $keyword) {
            $matchPhraseQueries = [];
            foreach ($searchFields as $field) {
                $matchPhraseQueries[] = [
                    'match_phrase' => [
                        $field => $keyword
                    ]
                ];
            }
            $exactKeywordsGroup[] = [ 'bool' => [ 'should' => $matchPhraseQueries]];
        }
        return $exactKeywordsGroup;
    }

    private function convertExactLeftKeywords($keywordLeft, $searchFields, $isSearchInRecentYears) {
        $keywordLeftGroup = [];
        $matchQueries = [];
        $matchQueries[] = [
            'multi_match' => [
                'query' => $keywordLeft,
                'type' => 'best_fields',
                'fields' => $searchFields,
                'analyzer' => 'english',
                'minimum_should_match' => '70%'
            ]
        ];

        $semanticQueries = [];
        if ($isSearchInRecentYears) {
            $semanticQueries[] = [
                'semantic' => [
                    'field' => 'semantic_text',
                    'query' => $keywordLeft
                ]
            ];
        }

        if (!empty($keywordLeft)) {
            $shouldQueries = array_merge($matchQueries, $semanticQueries);
            $keywordLeftGroup[] = [ 'bool' => [ 'should' => $shouldQueries]];
        }
        return $keywordLeftGroup;
    }

    private function convertMultiSearchKeywords($multiKeywords, $searchFields) {
        $multiKeywordsGroup = [];
        $weight = 50.0 / count($multiKeywords);
        foreach ($multiKeywords as $keyword) {
            $multiKeywordsGroup[] = [ 
                'constant_score' => [
                    'filter' => [ 
                        'bool' => [ 
                            'should' => [
                                [
                                    'multi_match' => [
                                        'query' => $keyword,
                                        'type' => 'best_fields',
                                        'fields' => $searchFields,
                                        'analyzer' => 'chinese',
                                        'minimum_should_match' => '50%'
                                    ]
                                ],
                                [
                                    'multi_match' => [
                                        'query' => $keyword,
                                        'type' => 'best_fields',
                                        'fields' => $searchFields,
                                        'analyzer' => 'english',
                                        'minimum_should_match' => '50%'
                                    ]
                                ],
                                [
                                    'wildcard' => [
                                        'attachment.content' => [
                                            'value' => "*$keyword*",
                                            'boost' => 1.5
                                        ]
                                    ]
                                ],
                                [
                                    'match' => [
                                        'attachment.content' => [
                                            'query' => $keyword,
                                            'operator' => 'or',
                                            'analyzer' => 'chinese'
                                        ]
                                    ]
                                ],
                                [
                                    'match' => [
                                        'title' => [
                                            'query' => $keyword,
                                            'fuzziness' => 'AUTO',
                                            'analyzer' => 'chinese'
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ], 
                    'boost' => $weight 
                ]
            ];
        }
        return $multiKeywordsGroup;
    }

    public function setPreviousPage() {
        $this->prevDisabled = true;
        if ($this->paginationData['current_page'] > 1) {
            $this->currentPage = $this->paginationData['current_page'] - 1;
            $this->previousPage();
        }
    }

    public function setNextPage() {
        $this->nextDisabled = true;
        if ($this->paginationData['current_page'] < $this->totalPage) {
            $this->currentPage = $this->paginationData['current_page'] + 1;
            $this->nextPage();
        }
    }

    public function updatedCurrentPage($value) {
        if ((int)$value < 1) {
            $value = 1;
        } else if ((int)$value > $this->totalPage) {
            $value = $this->totalPage;
        }
        $this->currentPage = $value;
        $this->changePage($value);
    }
}
