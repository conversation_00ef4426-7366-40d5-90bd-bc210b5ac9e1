<?php

namespace App\Livewire\ListOfGazette;

use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use App\Models\Gazette;

class ListOfGazettePage extends Component
{
    use WithPagination;

    public $isShow = true;
    public bool $pagination = true;
    public array $perPageOptions = [5, 20, 50];

    #[Url(as: 'page', except: '')] public int $page = 1;
    #[Url(as: 'per_page', except: '')] public int $perPage = 5;

    public $gazettes = [];
    public $paginationData = [];

    public function mount()
    {
        $this->isShow = true;
        $this->page = request()->query('page', 1);
        $this->perPage = request()->query('per_page', 5);
        if (!in_array($this->perPage, $this->perPageOptions)) {
            $this->perPage = $this->perPageOptions[0];
        }
    }

    public function render()
    {
        $result = [];

        // $gazettes = Gazette::with('mg', 'ls1', 'ls2', 'ls3', 'ls5')->orderBy('id', 'desc')->paginate($this->perPage, ['*'], 'page', $this->page);
        // $this->paginationData = [
        //     'current_page' => $gazettes->currentPage(),
        //     'last_page' => $gazettes->lastPage(),
        //     'per_page' => $gazettes->perPage(),
        //     'total' => $gazettes->total(),
        // ];

        // foreach ($gazettes as $gaz) {
        //     $gaz->ls4 = $gaz->ls4();
        //     $gaz->ls6 = $gaz->ls6();

        //     $gaz->dayDate = \DateTime::createFromFormat('Ymd', $gaz->dateno)->format('d');
        //     $gaz->monthDate = \DateTime::createFromFormat('Ymd', $gaz->dateno)->format('m');
        //     $gaz->yearDate = \DateTime::createFromFormat('Ymd', $gaz->dateno)->format('Y');
        //     $gaz->enDayOfWeek = \DateTime::createFromFormat('Ymd', $gaz->dateno)->format('l');
        //     $gaz->zhDayOfWeek = $this->getDayOfWeek(\DateTime::createFromFormat('Ymd', $gaz->dateno)->format('w'));
        //     $result[] = $gaz;
        // }
        $this->loadGazettes();

        return view('livewire.list-of-gazette.list-of-gazette-page', [
            'gazettes' => $result,
            'paginationData' => $this->paginationData,
            'page' => $this->page,
        ]);
    }

    public function getDayOfWeek($weekday) {
        $weekdays_text = array("星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六");
        return $weekdays_text[$weekday];
    }

    public function loadGazettes()
    {
        $this->paginationData = [
            'current_page' => 1,
            'last_page' => 1,
            'per_page' => $this->perPage,
            'total' => 0,
        ];
        $result = [];

        $gazettes = Gazette::with('mg', 'ls1', 'ls2', 'ls3', 'ls5')->where('status', 1)->orderBy('id', 'desc')->paginate($this->perPage, ['*'], 'page', $this->page);
        $this->paginationData = [
            'current_page' => $gazettes->currentPage(),
            'last_page' => $gazettes->lastPage(),
            'per_page' => $gazettes->perPage(),
            'total' => $gazettes->total(),
        ];

        foreach ($gazettes as $gaz) {
            $gaz->ls4 = $gaz->ls4();
            $gaz->ls6 = $gaz->ls6();

            $gaz->dayDate = \DateTime::createFromFormat('Ymd', $gaz->dateno)->format('d');
            $gaz->monthDate = \DateTime::createFromFormat('Ymd', $gaz->dateno)->format('m');
            $gaz->yearDate = \DateTime::createFromFormat('Ymd', $gaz->dateno)->format('Y');
            $gaz->enDayOfWeek = \DateTime::createFromFormat('Ymd', $gaz->dateno)->format('l');
            $gaz->zhDayOfWeek = $this->getDayOfWeek(\DateTime::createFromFormat('Ymd', $gaz->dateno)->format('w'));
            $result[] = $gaz;
        }

        $this->gazettes = $result;
    }

    #[On('update-property')]
    public function updateProperty($propertyName, $value): void
    {
        if ($propertyName == 'perPage') {
            if($this->perPage !== $value) {
                $this->page = 1;
                $this->paginationData['current_page'] = 1;
            }
            $this->perPage = $value;
            $this->loadGazettes();
        }
    }

    #[On('previous-page')]
    public function previousPage()
    {
        $this->page = max(1, (int)$this->page - 1);
        $this->loadGazettes();
    }

    #[On('next-page')]
    public function nextPage()
    {
        $this->page = min($this->paginationData['last_page'], (int)$this->page + 1);
        $this->loadGazettes();
    }

    #[On('change-page')]
    public function changePage($page)
    {
        $this->page = $page;
        $this->loadGazettes();
    }
}
