.gld-card {
    background: var(--grey-white);
    box-shadow: var(--shadow-base);
    border-radius: var(--radius-big);
}

.gld-card-article {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.gld-card-article-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}


.gld-card-title {
    color: var(--grey-900);
}

.gld-card-article-section-title {
    color: var(--primary-800);
}

#important-notices-form .gld-card-article-section-title {
    margin-bottom: var(--spacing-md);
}

#important-notices-form ol {
    padding-left: var(--spacing-x-big);
}

#important-notices-form ol li {
    font-weight: 700;
    list-style-position: outside;
}

a {
    text-decoration: none;
    color: var(--primary-600);
}

a:hover {
    text-decoration: underline;
}

@media (min-width: 992px) {
    .gld-card-article {
        gap: var(--spacing-xx-lg);
    }
}

@media print {
    /*.gld-card {*/
    /*    padding: 0 !important;*/
    /*}*/

    /*.gld-card-article {*/
    /*    gap: var(--spacing-md) !important;*/
    /*}*/

}
