<?php
$locale = app()->getLocale();
?>
<div class="gld-search-table-wrapper" xmlns:livewire="http://www.w3.org/1999/html">
    @if(isset($title) && !empty($title))
        <div class="section-header toggle-btn" wire:click="$toggle('isShow')">
            <h2 class="gld-highlight-title h2">{{ $title }}</h2>
            <button type="button" class="label1 expand-btn" title="{{ $isShow ? __('general.collapse') : __('general.expand') }}">
                <span class="sr-only">{{ $isShow ? __('general.collapse') : __('general.expand') }}</span>
                <span class="gld-svg-container {{ $isShow ? 'expand' : 'collapse' }}">
                            <span class="arrow-icon svg"></span>
                    </span>
            </button>
        </div>
    @endif
    {{-- expand-container --}}
    <div class="gld-default-table-container @if($expandable) expand-container @endif gld-search-table-container {{ $isShow ? 'open' : 'hide' }}">
        <div class="gld-search-table">
            @if (count($notices) == 0)
                <div class="table-empty-result">
                    -- {{ __('general.table.no_match') }} --
                </div>
            @else
                @foreach ($notices as $notice)
                    <div class="gld-search-table-col">
                    @if(in_array('1', $column))
                        <div class="gld-search-table-row">
                            <div class="left label2">{{ __('general.table.columns_cat') }}</div>
                            <div class="right label1">
                            @if ($notice->table_name == 'mg')
                                {{ __('general.search_gazette.category_gn') }}
                            @elseif ($notice->table_name == 'ls1' || $notice->table_name == 'ls2' || $notice->table_name == 'ls3' || $notice->table_name == 'ls5')
                                {{ __('general.search_gazette.category_ln') }}
                            @elseif ($notice->table_name == 'ls4')
                                {{ __('general.search_gazette.category_sn4') }}
                            @elseif ($notice->table_name == 'ls6')
                                {{ __('general.search_gazette.category_sn6') }}
                            @else
                                --
                            @endif
                            </div>
                        </div>
                    @endif
                    @if(in_array('2', $column))
                        <div class="gld-search-table-row">
                            <div class="left label2">{{ __('general.table.columns_date') }}</div>
                            <div class="right label1">
                                @if ($locale == 'en')
                                    {{ $notice->enDayOfWeek }}, {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }} {{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }}
                                @else
                                    {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ $notice->zhDayOfWeek }}<br/>{{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }} {{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }}
                                @endif
                            </div>
                        </div>
                    @endif
                    @if(in_array('3', $column))
                        <div class="gld-search-table-row">
                            <div class="left label2">{{ __('general.table.columns_num') }}</div>
                            <div class="right label1">
                                @if ($notice->table_name == 'mg')
                                    {{ $notice->notice_no }}
                                @else
                                    --
                                @endif
                            </div>
                        </div>
                    @endif
                    @if(in_array('7', $column))
                        <div class="gld-search-table-row">
                            <div class="left label2">{{ __('general.table.columns_in') }}</div>
                            <div class="right label1">
                                @if ($notice->table_name == 'ls1' || $notice->table_name == 'ls2' || $notice->table_name == 'ls3' || $notice->table_name == 'ls5')
                                    @if ($notice->notice_no == '999')
                                        {{ '--' }}
                                    @else
                                        {{ $notice->notice_no }}
                                    @endif
                                @else
                                    --
                                @endif
                            </div>
                        </div>
                    @endif
                    @if(in_array('4', $column))
                        <div class="gld-search-table-row">
                            <div class="left label2">{{ __('general.table.columns_ls') }}</div>
                            <div class="right label1">
                                @if ($notice->table_name == 'ls1' || $notice->table_name == 'ls2' || $notice->table_name == 'ls3' || $notice->table_name == 'ls5')
                                    {{ __('general.search_gazette.ls_group_' . $notice->table_name) }}
                                @else
                                    --
                                @endif
                            </div>
                        </div>
                    @endif
                        <div class="gld-search-table-row">
                            <div class="left label2">{{ __('general.table.columns_title') }}</div>
                            <div class="right label1">
                                <div class="title label1">
                                    @if ($notice->table_name == 'ls6')
                                        {{ __('general.search_gazette.group_title_' . $notice->e_title) }}
                                    @else
                                        @if (($notice->highlight_en ?? '') == '' && ($notice->highlight_zh ?? '') == '' && ($notice->highlight_title ?? '') != '')
                                            {!! $notice->highlight_title !!}
                                        @else
                                            @if (isset($notice->title) && $notice->table_name != 'ls4')
                                                {!! $notice->title !!}
                                            @else
                                                @if ($locale == 'en')
                                                    {!! $notice->e_title !!}
                                                @else
                                                    {!! $notice->c_title !!}
                                                @endif
                                            @endif
                                        @endif
                                    @endif
                                </div>
                                <div class="disc label2">
                                    @if ($locale == 'en')
                                        @if (($notice->highlight_en ?? '') != '')
                                            {!! $notice->highlight_en !!}
                                        @endif
                                    @else
                                        @if (($notice->highlight_zh ?? '') != '')
                                            {!! $notice->highlight_zh !!}
                                        @endif
                                    @endif
                                </div>
                            </div>
                        </div>
                    @if($isLs6)
                        <div class="gld-search-table-row">
                            <div class="left label2">{{ __('general.table.columns_class') }}</div>
                            <div class="right label1">
                                @if ($notice->table_name == 'ls6')
                                    {!! __('general.search_gazette.group_class_' . $notice->e_title) !!}
                                @else
                                    --
                                @endif
                            </div>
                        </div>
                    @endif
                    @if(in_array('5', $column))
                        <div class="gld-search-table-row">
                            <div class="left label2">{{ __('general.table.columns_iny') }}</div>
                            <div class="right label1">
                                @if ($notice->table_name == 'ls1' || $notice->table_name == 'ls2' || $notice->table_name == 'ls3' || $notice->table_name == 'ls5')
                                    @if ($notice->notice_no == '999')
                                        {{ '--' }}
                                    @else
                                        {{ __('general.search_gazette.iny_label') . ' ' . $notice->notice_no }}<br/>{{ $notice->year }}
                                    @endif
                                @else
                                    --
                                @endif
                            </div>
                        </div>
                    @endif
                    @if(in_array('6', $column))
                        <div class="gld-search-table-row">
                            <div class="left label2">{{ __('general.table.columns_io') }}</div>
                            <div class="right label1">
                                @if ($locale == 'en')
                                    {{ ($notice->e_position ?? '') == '' ? '--' : $notice->e_position }}
                                @else
                                    {{ ($notice->c_position ?? '') == '' ? '--' : $notice->c_position }}
                                @endif
                            </div>
                        </div>
                    @endif
                        <div class="gld-search-table-row">
                            <div class="left label2">{{ __('general.table.columns_eng') }}</div>
                            <div class="right label1">
                                <a class="gld-svg-container" href="{{ $notice->englishPdfUrl }}">
                                    <img src="{{ asset('images/file/pdf.svg') }}" alt="{{ __('general.table.download_pdf_en') }}" title="{{ __('general.table.download_pdf_en') }}">
                                </a>
                            </div>
                        </div>
                        <div class="gld-search-table-row">
                            <div class="left label2">{{ __('general.table.columns_chi') }}</div>
                            <div class="right label1">
                                <a class="gld-svg-container" href="{{ $notice->chinesePdfUrl }}">
                                    <img src="{{ asset('images/file/pdf.svg') }}" alt="{{ __('general.table.download_pdf_tc') }}" title="{{ __('general.table.download_pdf_tc') }}">
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>
        <table class="gld-default-table">
            <thead>
            <tr>
                @if(in_array('1', $column))
                    <th class="gld-default-table-header label2" style="min-width: 160px">{{ __('general.table.columns_cat') }}</th>
                @endif
                @if(in_array('2', $column))
                    <th class="gld-default-table-header label2" style="min-width: 200px">{{ __('general.table.columns_date') }}</th>
                @endif
                @if(in_array('3', $column))
                    <th class="gld-default-table-header label2" style="min-width: 160px">{{ __('general.table.columns_num') }}</th>
                @endif
                @if(in_array('7', $column))
                    <th class="gld-default-table-header label2" style="min-width: 160px">{{ __('general.table.columns_in') }}</th>
                @endif
                @if(in_array('4', $column))
                    <th class="gld-default-table-header label2" style="min-width: 160px">{{ __('general.table.columns_ls') }}</th>
                @endif
                <th class="gld-default-table-header label2" style="min-width: 460px; width: 100%">{{ __('general.table.columns_title') }}</th>
                @if($isLs6)
                    <th class="gld-default-table-header label2" style="min-width: 300px">{{ __('general.table.columns_class') }}</th>
                @endif
                @if(in_array('5', $column))
                    <th class="gld-default-table-header label2" style="min-width: 160px">{{ __('general.table.columns_iny') }}</th>
                @endif
                @if(in_array('6', $column))
                    <th class="gld-default-table-header label2" style="min-width: 200px">{{ __('general.table.columns_io') }}</th>
                @endif
                <th class="gld-default-table-header label2" style="min-width: 100px">{{ __('general.table.columns_eng') }}</th>
                <th class="gld-default-table-header label2" style="min-width: 100px">{{ __('general.table.columns_chi') }}</th>
            </tr>
            </thead>
            <tbody>
            @if (count($notices) == 0)
                <tr>
                    <td colspan="5">
                        <div class="table-empty-result">
                            -- {{ __('general.table.no_match') }} --
                        </div>
                    </td>
                </tr>
            @else
                @foreach ($notices as $notice)
                    <tr>
                        @if(in_array('1', $column))
                            <td class="gld-default-table-data label1">
                                @if ($notice->table_name == 'mg')
                                    {{ __('general.search_gazette.category_gn') }}
                                @elseif ($notice->table_name == 'ls1' || $notice->table_name == 'ls2' || $notice->table_name == 'ls3' || $notice->table_name == 'ls5')
                                    {{ __('general.search_gazette.category_ln') }}
                                @elseif ($notice->table_name == 'ls4')
                                    {{ __('general.search_gazette.category_sn4') }}
                                @elseif ($notice->table_name == 'ls6')
                                    {{ __('general.search_gazette.category_sn6') }}
                                @else
                                    --
                                @endif
                                <!-- @php
                                    if (config('app.env') == 'local' and isset($notice->score)) {
                                        echo ' (' . $notice->score . ')';
                                    }
                                @endphp -->
                            </td>
                        @endif
                        @if(in_array('2', $column))
                            <td class="gld-default-table-data label1">
                                @if ($locale == 'en')
                                    {{ $notice->enDayOfWeek }}, {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }} {{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }}
                                @else
                                    {{ __('general.search_gazette.date_format_table', ['day' => $notice->dayDate, 'month' => $notice->monthDate, 'year' => $notice->yearDate]) }}<br/>{{ $notice->zhDayOfWeek }}<br/>{{ __('general.search_gazette.year_volume', ['volume' => $notice->volume]) }} {{ __('general.search_gazette.date_issue', ['issue_num' => $notice->gno]) }}
                                @endif
                            </td>
                        @endif
                        @if(in_array('3', $column))
                            <td class="gld-default-table-data label1">
                                @if ($notice->table_name == 'mg')
                                    {{ $notice->notice_no }}
                                @else
                                    --
                                @endif
                            </td>
                        @endif
                        @if(in_array('7', $column))
                            <td class="gld-default-table-data label1">
                                @if ($notice->table_name == 'ls1' || $notice->table_name == 'ls2' || $notice->table_name == 'ls3' || $notice->table_name == 'ls5')
                                    @if ($notice->notice_no == '999')
                                        {{ '--' }}
                                    @else
                                        {{ $notice->notice_no }}
                                    @endif
                                @else
                                    --
                                @endif
                            </td>
                        @endif
                        @if(in_array('4', $column))
                            <td class="gld-default-table-data label1">
                                @if ($notice->table_name == 'ls1' || $notice->table_name == 'ls2' || $notice->table_name == 'ls3' || $notice->table_name == 'ls5')
                                    {{ __('general.search_gazette.ls_group_' . $notice->table_name) }}
                                @else
                                    --
                                @endif
                            </td>
                        @endif
                        <td class="gld-default-table-data label1">
                            <div class="title label1">
                                @if ($notice->table_name == 'ls6')
                                    {{ __('general.search_gazette.group_title_' . $notice->e_title) }}
                                @else
                                    @if (($notice->highlight_en ?? '') == '' && ($notice->highlight_zh ?? '') == '' && ($notice->highlight_title ?? '') != '')
                                        {!! $notice->highlight_title !!}
                                    @else
                                        @if (isset($notice->title) && $notice->table_name != 'ls4')
                                            {!! $notice->title !!}
                                        @else
                                            @if ($locale == 'en')
                                                {!! $notice->e_title !!}
                                            @else
                                                {!! $notice->c_title !!}
                                            @endif
                                        @endif
                                    @endif
                                @endif
                            </div>
                            <div class="disc label2">
                                @if ($locale == 'en')
                                    @if (($notice->highlight_en ?? '') != '')
                                        {!! $notice->highlight_en !!}
                                    @endif
                                @else
                                    @if (($notice->highlight_zh ?? '') != '')
                                        {!! $notice->highlight_zh !!}
                                    @endif
                                @endif
                            </div>
                        </td>
                        @if($isLs6)
                            <td class="gld-default-table-data label1">
                                @if ($notice->table_name == 'ls6')
                                    {!! __('general.search_gazette.group_class_' . $notice->e_title) !!}
                                @else
                                    --
                                @endif
                            </td>
                        @endif
                        @if(in_array('5', $column))
                            <td class="gld-default-table-data label1">
                                @if ($notice->table_name == 'ls1' || $notice->table_name == 'ls2' || $notice->table_name == 'ls3' || $notice->table_name == 'ls5')
                                    @if ($notice->notice_no == '999')
                                        {{ '--' }}
                                    @else
                                        {{ __('general.search_gazette.iny_label') . ' ' . $notice->notice_no }}<br/>{{ $notice->year }}
                                    @endif
                                @else
                                    --
                                @endif
                            </td>
                        @endif
                        @if(in_array('6', $column))
                            <td class="gld-default-table-data label1">
                                @if ($locale == 'en')
                                    {{ ($notice->e_position ?? '') == '' ? '--' : $notice->e_position }}
                                @else
                                    {{ ($notice->c_position ?? '') == '' ? '--' : $notice->c_position }}
                                @endif
                            </td>
                        @endif
                        <td class="gld-default-table-data label1">
                            <a class="gld-svg-container" href="{{ $notice->englishPdfUrl }}" target="_blank">
                                <img src="{{ asset('images/file/pdf.svg') }}" alt="{{ __('general.table.download_pdf_en') }}" title="{{ __('general.table.download_pdf_en') }}">
                            </a>
                        </td>
                        <td class="gld-default-table-data label1">
                            <a class="gld-svg-container" href="{{ $notice->chinesePdfUrl }}" target="_blank">
                                <img src="{{ asset('images/file/pdf.svg') }}" alt="{{ __('general.table.download_pdf_tc') }}" title="{{ __('general.table.download_pdf_tc') }}">
                            </a>
                        </td>
                    </tr>
                @endforeach
            @endif
            </tbody>
        </table>
    </div>
    @if($pagination)
        <livewire:table.pagination
            :$paginationData
            :$page
        />
    @endif
</div>
