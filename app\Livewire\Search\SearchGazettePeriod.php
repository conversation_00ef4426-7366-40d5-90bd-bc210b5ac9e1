<?php

namespace App\Livewire\Search;

use Livewire\Component;
use Livewire\Attributes\Modelable;
use App\Models\SearchGazettePeriod as SearchGazettePeriodModel;
class SearchGazettePeriod extends Component
{
    public $displayPeriod = [];

    public function mount() 
    {
        $this->displayPeriod = SearchGazettePeriodModel::getDisplayPeriod();
    }

    public function render()
    {
        return view('livewire.search.search-gazette-period');
    }
}
