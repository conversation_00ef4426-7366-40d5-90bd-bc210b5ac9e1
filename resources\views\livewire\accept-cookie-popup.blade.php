<div>
    @if ($show)
        <style>
            .cookie-popup {
                width: 100%;
                background: var(--grey-white);
                position: fixed;
                bottom: -100px;
                z-index: 999;
                padding: var(--spacing-big);
                box-shadow: var(--shadow-md);
                animation: slide-up 1s forwards;
            }
            .gld-cookie-container {
                max-width: 1440px;
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                gap: var(--spacing-x-big);
            }
            .cookie-buttons {
                display: flex;
                justify-content: center;
                gap: var(--spacing-md);
            }

            @keyframes slide-up {
                0% {
                    bottom: -100px;
                }
                100% {
                    bottom: 0;
                }
            }

            @media (min-width: 992px) {
                .cookie-popup {
                    padding: var(--spacing-xx-big) var(--spacing-lg);
                }
                .gld-cookie-container {
                    flex-direction: row;
                    margin: auto;
                }
            }
        </style>
        <div class="overlay"></div>
        <div class="cookie-popup">
            <div class="gld-cookie-container">
            <span class="body1">
                {!! $data['cookie'] !!}
            </span>
                <div class="cookie-buttons">
                    <button wire:click="rejectCookies" id="reject-button" class="gld-outline-button label1">
                        {{ __('general.button.reject') }}
                    </button>
                    <button wire:click="acceptCookies" id="accept-button" class="gld-button label1">
                        {{ __('general.button.accept') }}
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>
