.arrow-icon::after {
    mask: url("/images/icon/arrow_top.svg") no-repeat;
    mask-size: cover;
}

.expand-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--primary-50);
    min-width: 28px;
    min-height: 28px;
    border-radius: var(--radius-x-sm);
    border: none;
    transition: 0.2s background ease;
}

.expand-btn:hover {
    background: var(--primary-100);
}

.expand-btn .gld-svg-container {
    width: 18px;
    height: 18px;
    color: var(--grey-900);
}

.expand-btn .gld-svg-container.collapse {
    transform: rotate(180deg);
    transition: transform 0.2s ease;
}

.expand-btn .gld-svg-container.expand {
    transform: rotate(0);
    transition: transform 0.2s ease;
}
