<div class="search-gazette-filter-wrapper">
    <div class="search-gazette-filter-header toggle-btn" wire:click="$toggle('isShowFilter')">
        <div class="search-gazette-filter-title">
            <div class="gld-svg-container">
                <span class="filter-icon svg" style="color:#2563EB"></span>
            </div>
            <h2 class="label1" style="color:#2563EB">{{ __('general.search_gazette.filter') }}</h2>
        </div>
        <button type="button" class="label1 expand-btn" title="{{ $isShowFilter ? __('general.collapse') : __('general.expand') }}">
            <span class="sr-only">{{ $isShowFilter ? __('general.collapse') : __('general.expand') }}</span>
            <span class="gld-svg-container {{ $isShowFilter ? 'expand' : 'collapse' }}">
                <span class="arrow-icon svg" style="color:#2563EB"></span>
            </span>
        </button>
    </div>
    <form class="search-gazette-filter-options expand-container {{ $isShowFilter ? 'open' : 'hide' }}" id="filter-form">
        <div class="search-gazette-filter-options-container">
            <div id="filter-options-period">
                <label class="label2 filter-option-label">{{ __('general.search_gazette.period') }}</label>
                <livewire:search.search-gazette-period wire:model.live="period"/>
            </div>
            <div class="filter-selection">
                <livewire:input.select.default-select
                    :showHideContent="'.search-gazette-filter-options'"
                    name="yearAndVolume"
                    updateFn="update-property"
                    id="filter-options-year-vol"
                    label="{{ __('general.search_gazette.year_and_volume') }}"
                    :type="['button' => 'default', 'options' => 'default']"
                    :selected="$yearAndVolume"
                    :disabled="empty($category)"
                    :options="$yearAndVolumeOptions"
                />
                <livewire:input.select.default-select
                    :showHideContent="'.search-gazette-filter-options'"
                    name="dateAndIssue"
                    updateFn="update-property"
                    id="filter-options-date-issues"
                    label="{{ __('general.search_gazette.date_and_issue') }}"
                    :type="['button' => 'default', 'options' => 'default']"
                    :selected="$dateAndIssue"
                    :disabled="$this->checkYearIsInvalid() || empty($category)"
                    :options="$dateAndIssueOptions"
                />
            </div>
            <div id="filter-options-category">
                <label class="label2 filter-option-label">{{ __('general.search_gazette.search_category') }}</label>
                <fieldset class="filter-options-category-container">
                    <legend class="sr-only">Select Category</legend>
                    @foreach([
                        1 => ['label' => 'government_notice_title', 'shortName' => 'mg'],
                        3 => ['label' => 'supplement_no_4_title', 'shortName' => 'ls4'],
                        2 => ['label' => 'legal_notice_title', 'shortName' => 'ls'],
                        4 => ['label' => 'supplement_no_6_title', 'shortName' => 'ls6'],
                      ] as $key => $category
                    )
                        <div class="label2 search-gazette-category-button">
                            <input type="checkbox" name="category" id="category-{{ $key }}" value="{{ $key }}" wire:model.live="category" />
                            <label for="category-{{ $key }}">
                                <span class="checkmark"></span>
                                {{ __('general.home.' . $category['label']) }}
                                <span class="category-total body2" x-data x-show="$wire.category.includes('{{ $key }}')">
                                    {{ $categoriesResultCount[$category['shortName']] ?? '' }}
                                </span>
                            </label>
                        </div>
                    @endforeach
                </fieldset>
            </div>
            <div class="filter-selection">
                <livewire:input.select.default-select
                    :showHideContent="'.search-gazette-filter-options'"
                    name="supplementNo"
                    updateFn="update-property"
                    id="filter-options-supplement"
                    label="{{ __('general.search_gazette.legal_supplement_no') }}"
                    :type="['button' => 'default', 'options' => 'default']"
                    :selected="$supplementNo"
                    :disabled="$this->checkCategoryIsNotSelect(['2']) || empty($category)"
                    :options="['' => __('general.search_gazette.pls_select'),
                      '1' => '1',
                      '2' => '2',
                      '3' => '3',
                      '5' => '5']"
                />
                <livewire:input.select.default-select
                    :showHideContent="'.search-gazette-filter-options'"
                    name="group"
                    updateFn="update-property"
                    id="filter-options-group"
                    label="{{ __('general.search_gazette.group') }}"
                    :type="['button' => 'default', 'options' => 'default']"
                    :selected="$group"
                    :disabled="$this->checkCategoryIsNotSelect(['4']) || empty($category)"
                    :options="['' => __('general.search_gazette.pls_select'),
                      '1' => __('general.search_gazette.group_title_1'),
                      '2' => __('general.search_gazette.group_title_2'),
                      '3' => __('general.search_gazette.group_title_3'),
                      '4' => __('general.search_gazette.group_title_4'),
                      '5' => __('general.search_gazette.group_title_5'),
                      '6' => __('general.search_gazette.group_title_6')]"
                />
            </div>
            <div class="search-gazette-filter-buttons">
                <button type="button" class="label1 gld-dashed-button" wire:click.prevent="resetFilters">{{ __('general.search_gazette.reset') }}</button>
                <button type="button" class="label1 gld-button submit-button" onclick="handleFilterSubmit()">
                    <span class="gld-svg-container">
                        <span class="search-icon svg"></span>
                    </span>
                    {{ __('general.search_gazette.apply_filters') }}
                </button>
            </div>
        </div>
    </form>
</div>
@script
<script>
    $wire.$watch('isShowFilter', () => {
        const expandContainer = document.querySelector('.expand-container');
        document.querySelector('.expand-container').style.maxHeight = document.querySelector('.expand-container').scrollHeight + 'px';
        expandContainer.style.maxHeight = expandContainer.scrollHeight + 'px';
        if ($wire.$get('isShowFilter')) {
            expandContainer.style.maxHeight = expandContainer.scrollHeight + 'px';
        } else {
            expandContainer.style.maxHeight = '0';
        }
    });

    // Handle form submission with current URL
    window.handleFilterSubmit = function() {
        document.documentElement.style.overflow = 'hidden';
        $wire.submitFilterData(window.location.href);
    };

</script>
@endscript

