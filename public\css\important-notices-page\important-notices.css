@import url("../default-pages/default-pages.css");
@import url("../component/card/card.css");
@import url("../component/text/highlight-title.css");
@import url("../component/button/arrow-button.css");

.gld-card {
    padding: var(--spacing-big);
    box-shadow: var(--shadow-md);
}

a {
    word-break: break-word;
    font-size: 0.875rem;
    line-height: 150%;
    letter-spacing: 0;
    font-weight: 600;
}

.accept-notice-checkbox {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
    cursor: pointer;
    width: fit-content;
}

.cf-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    justify-content: space-between;
}

.cf-turnstile {
    width: 100%;
}

.button-set .gld-dashed-button {
    height: 56px;
}

.button-set {
    display: flex;
    align-items: center;
    gap: 24px;
    justify-content: space-between;
}

#important-notices-body ol {
    list-style-position: inside;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
}

#important-notices-body ul {
    list-style: disc;
    margin-left: 22px;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 150%;
}


@media (min-width: 992px) {
    .cf-container {
        flex-direction: row;
    }

    .gld-card {
        padding: var(--spacing-xxx-big) var(--spacing-xxx-lg);
        box-shadow: var(--shadow-md);
    }

     a {
        font-size: 1.125rem;
        line-height: 150%;
        letter-spacing: 0;
        font-weight: 600;
    }

}
