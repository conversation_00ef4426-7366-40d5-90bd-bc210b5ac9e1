@import url("../default.css");
@import url("../component/button/arrow-button.css");
@import url("../component/text/highlight-title.css");
@import url("../contact-us-page/contact-us-icons.css");

#search-all-gazette {
    grid-column: 1 / span 2;
    grid-row: 1;
}

#search-list-of-gazette-gazette {
    grid-column: 1 / span 2;
    grid-row: 2;
}

#supplement {
    grid-column: 1 / span 2;
    grid-row: 3;
}

#gov-notice {
    grid-column: 1;
    grid-row: 1;
}

#legal-notice {
    grid-column: 2;
    grid-row: 1;
}

#supplement-no-4 {
    grid-column: 1;
    grid-row: 2;
}

#supplement-no-6 {
    grid-column: 2;
    grid-row: 2;
}

#homepage-banner {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: fit-content;
    position: relative;
    background-size: cover !important;
    gap: 40px;
    min-height: 435px;
    justify-content: space-between;
    /*padding-bottom: 60px;*/
}

#homepage-banner #homepage-banner-heading {
    color: var(--primary-800);
}

#homepage-banner .gld-banner-left {
    padding-right: var(--spacing-big);
    max-width: 480px;
}

#homepage-banner .gld-banner-left .gld-banner-slogan-box {
    margin-top: calc(var(--header-height-sm) + var(--spacing-big));
    bottom: var(--spacing-huge);
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: var(--spacing-xxx-big);
    padding: var(--spacing-big);
    background: rgba(250, 250, 250, 0.85);
    box-shadow: 5px 5px 6px 0 var(--warning-600);
}

#homepage-banner .gld-banner-right {
    margin-left: auto;
    width: 95%;
    max-width: 400px;
    background: var(--grey-50);
    padding: var(--spacing-md);
}

#homepage-banner .gld-banner-right .gld-banner-desc {
    max-width: 100%;
    width: 100%;
    font-style: italic;
}

.banner-fs {
    font-size: 24px;
    line-height: 110%;
    letter-spacing: 0;
    font-weight: 400;
    font-family: "DM Serif Display", serif;
}

.gld-searchbar {
    height: 56px;
    max-width: 284px;
    width: 100%;
    transition: all 0.3s ease-in-out;
}

.gld-searchbar:hover {
    background: var(--primary-800);
    /*box-shadow: 0 10px 20px rgba(12, 63, 173, 0.3);*/
    transition: all 0.3s ease-in-out;
}

.gld-searchbar:hover .search-icon {
    /*animation: scale 0.5s ease-in-out;*/
    transform: scale(1.125);
    transition: transform 0.3s ease-in-out;
}

.gld-searchbar .search-icon {
    transition: transform 0.3s ease-in-out;
}

@keyframes scale {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.gld-searchbar {
    height: 56px;
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    background: var(--primary-600);
    border-radius: var(--radius-full);
    outline: none;
    border: none;
    padding: 0 calc(14px + 44px + 10px) 0 var(--spacing-x-big);
    color: var(--grey-white);
    text-decoration: none;
}

.gld-searchbar .gld-svg-container {
    min-width: 44px;
    min-height: 44px;
    width: 44px;
    height: 44px;
    color: var(--primary-600);
    display: flex;
    position: absolute;
    right: 8px;
    background: var(--grey-white);
    padding: var(--spacing-sm);
    border-radius: var(--radius-full);
    border: none;
}

.homepage-notice-supplement-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-x-big);
}

#homepage-gazette-notice .gld-main-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxx-big);
    align-items: center;
    text-align: center;
}

#homepage-gazette-notice .gld-section-heading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

#homepage-gazette-notice .gld-section-heading p {
  color: var(--grey-600);
}

#homepage-gazette-notice .gld-highlight-title {
    display: flex;
    flex-direction: column;
    align-items: center;
}

#homepage-gazette-notice .gld-highlight-title:before {
    margin-bottom: var(--spacing-xx-sm);
}

#homepage-gazette-notice .gld-section-content {
    width: 100%;
    max-width: 831px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: var(--spacing-x-big);
}

.category-box {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    background: var(--primary-600);
    box-shadow: var(--shadow-md);
    border-radius: var(--radius-x-sm);
    padding: var(--spacing-big);
    color: var(--grey-white);
    text-decoration: none !important;
    text-align: left;
    align-items: center;
    gap: var(--spacing-xx-big);
    transition: transform 0.3s ease-in-out;
}

.category-box .category-arrow-button .gld-svg-container {
  transition: transform 0.3s ease-in-out;
}

.category-box .category-text {
    margin-right: auto;
}

.category-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-2xl);
    transition: transform 0.3s ease-in-out;
}

.category-box:hover .gld-svg-container {
    transform: translateX(4px);
    transition: transform 0.3s ease-in-out;
}


.category-box .category-title {
    color: var(--grey-white);
}

.category-box .gld-img-container {
    min-width: 110px;
    min-height: 110px;
    width: 110px;
    height: 110px;
}

.category-box .category-desc {
    padding-right: 40px;
    color: #f0f0f0;
}

.category-box .category-arrow-button {
    display: flex;
    position: absolute;
    align-items: center;
    justify-content: center;
    right: 0;
    bottom: 0;
    min-width: 40px;
    min-height: 40px;
    width: 40px;
    height: 40px;
    background: var(--grey-white);
    border-bottom-right-radius: var(--radius-x-sm);
  }
.category-box .category-arrow-button .gld-svg-container {
    min-width: 20px;
    min-height: 20px;
    width: 20px;
    height: 20px;
    color: var(--primary-600);
}

.category-box .gazette-notice-latest::after {
  content: attr(data-content);
    height: fit-content;
    font-size: 1rem;
    font-weight: 600;
    background: var(--warning-600);
    padding: var(--spacing-xx-sm) var(--spacing-sm);
    margin-left: var(--spacing-xx-sm);
    border-radius: var(--radius-full);
    vertical-align: middle;
}


#contact-us {
    background-color: var(--grey-50);
    position: relative;
    overflow: hidden;
    background-repeat: no-repeat;
    background-size: cover;
}
#contact-us .gld-main-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--spacing-big);
}

#contact-us .circle-arrow-right-button {
    color: var(--primary-600);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--primary-600);
    width: 30px;
    height: 30px;
    min-width: 30px;
    min-height: 30px;
    background-color: var(--grey-white);
    border-radius: var(--radius-full);
}

.circle-arrow-right-button .gld-svg-container {
    min-width: 20px;
    min-height: 20px;
    width: 20px;
    height: 20px;
    color: inherit;
    transition: transform 0.3s ease-in-out;
}

#contact-us .contact-us-left {
    display: flex;
    width: 100%;
    justify-content: center;
}

#contact-us .contact-us-right {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxx-big);
    width: 100%;
}
#contact-us .contact-us-right #homepage-address {
    width: 100%;
    flex-grow: 1;
}

#contact-us .contact-us-right #homepage-general-enquiries {
    width: 100%;
    flex-grow: 1;
}

#contact-us .contact-us-right .contact-us-list-title {
    margin-bottom: var(--spacing-md);
}

#contact-us .contact-us-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.contact-us-link {
    text-decoration: none !important;
    display: flex;
    align-items: baseline;
    gap: var(--spacing-big);
}
.contact-us-link:hover .gld-svg-container {
    transform: translateX(3px);
    transition: transform 0.3s ease-in-out;
}


@media (min-width: 576px) {
    .category-box {
        flex-direction: row;
        padding: var(--spacing-lg) var(--spacing-xx-big);
    }

    .homepage-notice-supplement-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: auto auto;
        gap: var(--spacing-big);
    }
    .homepage-notice-supplement-container .category-box {
        max-width: 450px;
        flex-direction: column;
        align-items: start;
    }

    #contact-us {
        background-color: var(--grey-50);
        position: relative;
        overflow: hidden;
    }

    #contact-us .gld-main-container {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
    }
}

@media (min-width: 768px) {
    #contact-us {
         background-repeat: no-repeat;
         background-position: right;
         height: 100%;
         background-size: 90vw 100%;
     }

    #contact-us .gld-main-container {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-xx-big);
    }

    #contact-us .circle-arrow-right-button {
        width: 40px;
        height: 40px;
        min-width: 40px;
        min-height: 40px;
    }

    #contact-us .contact-us-right {
        flex-direction: row;
        gap: var(--spacing-xx-lg);
    }

    #contact-us .contact-us-right #homepage-address {
        width: 426px;
    }

    #contact-us .contact-us-right #homepage-general-enquiries {
        width: 300px;
    }
}

@media (min-width: 992px) {
    .banner-fs {
        font-size: 32px;
    }

    #homepage-banner {
        display: grid;
        grid-template-columns: 641px 1fr;
        height: 810px;
        gap: 0;
    }

    #homepage-banner .gld-banner-left {
        display: flex;
        align-items: center;
        position: relative;
        max-width: 100%;
    }

    #homepage-banner .gld-banner-left .gld-banner-slogan-box {
        margin-top: 0;
        /*position: absolute;*/
        position: relative;
        /*bottom: var(--spacing-xx-huge);*/
        bottom: 150px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: var(--spacing-xxx-big);
        padding: var(--spacing-x-big) var(--spacing-lg) var(--spacing-x-big) var(--spacing-container);
        background: rgba(250, 250, 250, 0.85);
        box-shadow: 5px 5px 6px 0 var(--warning-600);
    }

    #homepage-banner .gld-banner-right {
        position: absolute;
        max-width: unset;
        width: fit-content;
        bottom: 0;
        right: 0;
        padding: var(--spacing-x-big) var(--spacing-lg);
    }

    #homepage-banner .gld-banner-right .gld-banner-desc {
        max-width: 421px;
    }

    #homepage-gazette-notice .gld-main-container {
        padding: var(--spacing-xx-lg) var(--spacing-container);
    }
}

@media (min-width: 1232px) {
    #contact-us .contact-us-left {
        display: inline-block;
        width: fit-content;
    }

    #contact-us .contact-us-right {
        width: auto;
    }
}


@media print {
  /*#homepage-banner,*/
  /*.category-icon {*/
  /*  display: none !important;*/
  /*}*/

  /*.gazette-notice-latest::after {*/
  /*  background: var(--grey-50) !important;*/
  /*}*/

  /*.category-box {*/
  /*  padding: 0 !important;*/
  /*  background: var(--grey-white) !important;*/
  /*}*/

}
