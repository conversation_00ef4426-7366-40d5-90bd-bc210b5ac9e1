<?php

namespace App\Livewire\WhatsNew;

use App\Models\WebsiteContent;
use Livewire\Component;

class WhatsNewPage extends Component
{
    public array $data = [];
    public array $resourceKeys = [
        'table_title',
        'p1',
        'p2',
        'resource_title',
        'pnsp_title',
        'pnsp_p',
        'pnsp_url',
        'charges_title',
        'charges_p',
        'charges_url',
        'purchase_title',
        'purchase_p',
        'purchase_tel',
        'purchase_bookstore',
    ];

    public function render()
    {
        $locale = app()->getLocale();
        $content = $locale . '_content';

        $contents = WebsiteContent::where('page_name', 'WhatsNew')
            ->get()
            ->pluck($content, 'key');

        $classes = [
            'deadlinesIntro' => [
                '<h2>' => '<h2 class="gld-highlight-title h2">',
                '<p>'  => '<p class="body1">',
            ],
            'deadlineTable' => [
                '<table>' => '<table class="gld-default-table">',
                '<th>' => '<th class="gld-default-table-header label2">',
                '<td>'  => '<td class="gld-default-table-data label1">',
            ],
            'resourcesPurchaseContent' => [
                '<strong>' => '<strong class="body1 font-weight-600">',
                '<p>'  => '<p class="body1">',
            ],
            'table' => [
                '<table>' => '<table class="gld-default-table">',
                '<th>' => '<th class="gld-default-table-header label2">',
                '<td>'  => '<td class="gld-default-table-data label1">',
            ],
        ];

        foreach ($classes as $key => $class) {
            if ($key === 'deadlineTable' || $key === 'table') {
                $new_content =  preg_replace("/<table[^>]*>/i", "<table>", html_entity_decode($contents[$key] ?? ""));
                $new_content =  preg_replace("/<tr[^>]*>/i", "<tr>", $new_content);
                $this->data[$key] = addClasses($new_content ?? '', $class);
                continue;
            }
            $this->data[$key] = addClasses($contents[$key] ?? '', $class);
        }

        foreach ($this->resourceKeys as $key) {
            $this->data[$key] = html_entity_decode($contents[$key]) ?? '';
        }

        return view('livewire.whats-new.whats-new-page');
    }
}
