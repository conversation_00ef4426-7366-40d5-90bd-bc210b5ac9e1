<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\Ls4;

use Datetime;
use Illuminate\Support\Facades\URL;

class PdfController extends Controller
{
    public function view(Request $request)
    {
        $app_env = config('app.env');
        // Generate the file path based on the request parameters
        $type = $request->type ?? '';
        $year = $request->year ?? '';
        $volume = $request->volume ?? '';
        $gno = $request->gno ?? '';
        $notice_no = $request->notice_no ?? '';
        $extra = $request->extra ?? '0';
        $nid = $request->nid ?? '';
        $language = $request->language ?? 'en';
        $group = $request->group ?? '';

        $locale = app()->getLocale();

        if (!session('confirmed_view_gazette')) {
            header('Location: ' . route('important-notices', ['locale' => $locale]) . '?showCaptcha=1');
            exit;
        }

        if ($type == 'ls4') {
            $ls4 = Ls4::find($nid);
            if ($language == 'en') {
                // $filePath = "egazette/{$year}{$volume}{$gno}/{$ls4->e_filename}";
                $filePath = "{$year}{$volume}{$gno}/{$ls4->e_filename}";
            } else {
                // $filePath = "egazette/{$year}{$volume}{$gno}/{$ls4->c_filename}";
                $filePath = "{$year}{$volume}{$gno}/{$ls4->c_filename}";
            }
        } else if ($type == 's6') {
            // $filePath = "egazette/{$year}{$volume}{$gno}/{$type}{$year}{$volume}{$gno}{$group}.pdf";
            $filePath = "{$year}{$volume}{$gno}/{$type}{$year}{$volume}{$gno}{$group}.pdf";
        } else {
            $isExtra = $extra == 1 ? 'e' : '';
            // $filePath = "egazette/{$year}{$volume}{$gno}{$isExtra}/{$type}{$year}{$volume}{$gno}{$notice_no}.pdf";
            $filePath = "{$year}{$volume}{$gno}{$isExtra}/{$type}{$year}{$volume}{$gno}{$notice_no}.pdf";
        }

        if ($app_env === 'local') {
            if (!file_exists(public_path("storage/egazette/{$filePath}"))) {
                abort(404, 'File not found.');
            }

            $pdfUrl = asset("storage/egazette/{$filePath}");
        } else {
            if (storage::disk('object_storage')->exists($filePath)) {
                // $pdfUrl = Storage::disk('object_storage')->url($filePath);
                // $pdfUrl = Storage::temporaryUrl($filePath, now()->addMinutes(5),
                //     [
                //       'ResponseContentType' => 'application/octet-stream',
                //       'ResponseContentDisposition' => 'attachment; filename=file2.jpg',
                //   ]);
                // $pdfUrl = Storage::temporaryUrl($filePath, now()->addMinutes(5));

                // $pdfUrl = Storage::disk('object_storage')->temporaryUrl($filePath, now()->addMinutes(5));
                // print_r(parse_url($pdfUrl)); die();
                $tempPdfUrl = Storage::disk('object_storage')->temporaryUrl($filePath, now()->addMinutes(5));
                // $pdfUrl = config('app.url') . '/os' . parse_url($tempPdfUrl)['path'] . '?' . parse_url($tempPdfUrl)['query'] . '&locale=' . $locale;
                $pdfUrl = config('app.url') . '/os' . parse_url($tempPdfUrl)['path'] . '?' . parse_url($tempPdfUrl)['query'];
            } else {
                abort(404, 'File not found.');
            }
        }

        // return redirect(asset('pdfjs/web/viewer.html') . '?file=' . urlencode($pdfUrl) . '&locale=' . $locale);
        return redirect(asset('pdfjs/web/viewer.html') . '?file=' . urlencode($pdfUrl));
    }

    // public function show($type, $year, $volume, $gno, $notice_no, $extra, $nid, $language)
    public function show(Request $request)
    {
        $type = $request->type ?? '';
        $year = $request->year ?? '';
        $volume = $request->volume ?? '';
        $gno = $request->gno ?? '';
        $notice_no = $request->notice_no ?? '';
        $extra = $request->extra ?? '0';
        $nid = $request->nid ?? '';
        $language = $request->language ?? 'en';
        $group = $request->group ?? '';

        if (!session('confirmed_view_gazette')) {
            $locale = app()->getLocale();
            // Redirect to important notices if the condition is met
            header('Location: ' . route('important-notices', ['locale' => $locale]) . '?showCaptcha=1');
            exit;
        }

        if ($type == 'ls4') {
            $ls4 = Ls4::find($nid);
            if ($language == 'en') {
                $filePath = "storage/egazette/{$year}{$volume}{$gno}/{$ls4->e_filename}";
            } else {
                $filePath = "storage/egazette/{$year}{$volume}{$gno}/{$ls4->c_filename}";
            }
        } else if ($type == 's6') {
            $filePath = "storage/egazette/{$year}{$volume}{$gno}/{$type}{$year}{$volume}{$gno}{$group}.pdf";
        } else {
            $isExtra = '';
            if ($extra == 1) {
                $isExtra = 'e';
            }
    
            $filePath = "storage/egazette/{$year}{$volume}{$gno}{$isExtra}/{$type}{$year}{$volume}{$gno}{$notice_no}.pdf";
        }

        if (file_exists(public_path($filePath))) {
            // return response()->file(public_path($filePath));
            return response()->file(public_path($filePath), [
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0',
            ]);
        } else {
            abort(404, 'File not found.');
        }
    }
}
