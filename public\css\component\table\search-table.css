@import url("../../component/table/default-table.css");

.gld-search-table-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    overflow: hidden;
}

.gld-search-table-container  table {
    display: none;
}

.gld-search-table-container  .title {
    color: var(--primary-600);
}

.gld-search-table-container  .disc {
    margin-top: var(--spacing-x-sm);
    color: var(--grey-600);
}

.gld-search-table-container  .gld-svg-container {
    display: block;
    width: 32px;
    height: 32px;
}

.gld-search-table {
    display: flex;
    flex-direction: column;
}

.gld-search-table .gld-search-table-col {
    display: flex;
    flex-direction: column;
}

.gld-search-table .gld-search-table-col:not(:last-child) {
    border-bottom: 1px solid var(--grey-200);
}

.gld-search-table .gld-search-table-row {
    display: flex;
    flex-direction: row;
}

.gld-search-table .gld-search-table-row:not(:last-child) {
    border-bottom: 1px solid var(--grey-50);
}

.gld-search-table .gld-search-table-row .left {
    min-width: 105px;
    width: 105px;
    background-color: var(--primary-50);
    padding: var(--spacing-x-sm);
}

.gld-search-table .gld-search-table-row .right {
    width: 100%;
    color: var(--grey-600);
    background-color: var(--grey-white);
    padding: var(--spacing-x-sm);
}


.search-highlight {
    position: relative;
    z-index: 0;
    color: inherit;
    display: inline-block;
}

.search-highlight::after {
    position: absolute;
    content: "";
    display: inline-block;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    background-color: rgba(220, 38, 38, 0.20);
}

@media (min-width: 768px) {
    .gld-search-table {
        display: none;
    }

    .gld-search-table-container {
        overflow-x: auto;

    }

    .gld-search-table-container table {
        display: table;
    }

    .gld-search-table-container.gld-svg-container {
        width: 40px;
        height: 40px;
    }
}

@media print {
    /*.gld-search-table.gld-search-table-row .left {*/
    /*    background-color: var(--grey-50);*/
    /*}*/
}
