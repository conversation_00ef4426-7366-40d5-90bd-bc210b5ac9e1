<!DOCTYPE html>
<html lang="en" data-font-size="md">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>The Government of the Hong Kong Special Administrative Region Gazette - Maintenance Notice</title>
    <link rel="stylesheet" href="{{ asset('css/default.css') }}" />
    <style>
        #maintenance-notice {
            padding: var(--spacing-xxx-big) var(--spacing-big);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            width: 100%;

            .background-image {
                position: fixed;
                top: 0;
                left: 0;
                z-index: -1;
                width: 100%;
                height: 100%;
                background: url('{{ asset('images/homepage/home-banner.png') }}') no-repeat center;
                background-size: cover;
                opacity: 0.5;
            }

            .gld-img-container {
                width: 100%;
                max-width: {{ app()->getLocale() === 'zh' ? '300px' : '480px' }};
            }

            .gld-white-box {
                width: 100%;
                min-height: 50vh;
                max-width: 600px;
                padding: var(--spacing-x-big);
                border-radius: var(--radius-big);
                background: var(--grey-white);
                box-shadow: var(--shadow-md);
                display: flex;
                flex-direction: column;
                align-content: center;
                justify-content: center;
                text-align: center;
                gap: var(--spacing-sm);

                .gld-white-box-title {
                    color: var(--primary-800);
                }
            }

            .gld-white-box-icon {
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;

                .gld-img-container {
                    width: 100px;
                    height: 100px;
                }
            }
        }

        @media (min-width: 768px) {
            #maintenance-notice {
                padding: var(--spacing-xxx-lg);

                .gld-white-box {
                    min-height: 420px;
                    padding: var(--spacing-xxx-big) var(--spacing-lg);
                    gap: var(--spacing-md);

                    .gld-white-box-title {
                        color: var(--primary-800);
                    }
                }

            }
        }
    </style>
</head>
<body id="maintenance-notice">
<div class="background-image"></div>
<div class="gld-img-container">
    <img src="{{ app()->getLocale() === 'zh' ? asset('images/header/svg/HKSAR-Logo.svg') : asset('images/header/svg/HKSAR-Logo.svg') }}" alt="The Government of the Hong Kong Special Administrative Region Gazette" title="The Government of the Hong Kong Special Administrative Region Gazette"/>
</div>
<livewire:notice.maintenance/>
</body>
</html>

