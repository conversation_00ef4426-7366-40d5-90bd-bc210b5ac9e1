<?php

namespace App\Livewire\Search;

use Livewire\Component;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Http\Request;

use Livewire\Attributes\Reactive;

class SearchGazetteBar extends Component
{
    public string $searchKeyword = '';
    public array $keywords = [];
    public string $p = '';
    public string $c = '';
    public string $yv = '';
    public string $di = '';
    public string $sn = '';
    public string $g = '';
    public string $lang =  '';

    public function mount()
    {
        $this->searchKeyword = request()->query('kw', '');
        $this->p = request()->query('p', '');
        $this->c = request()->query('c', '');
        $this->yv = request()->query('yv', '');
        $this->di = request()->query('di', '');
        $this->sn = request()->query('sn', '');
        $this->g = request()->query('g', '');
        $this->lang = app()->getLocale();

        $this->keywords = json_decode(Cookie::get('search_keywords', '[]'), true);
    }

    public function clearSearchCookie() : void
    {
        Cookie::queue(Cookie::forget('search_keywords'));
        $this->keywords = [];
    }

    public function saveCookie(string $searchKeyword) : void
    {
        $searchKeyword = trim($searchKeyword);
        $cookieConsent = Cookie::get('cookie_consent');
        if ($cookieConsent === '1' && !empty(trim($searchKeyword))) {
            $keywords = json_decode(Cookie::get('search_keywords', '[]'), true);

            if (($key = array_search($searchKeyword, $keywords)) !== false) {
                unset($keywords[$key]);
            }

            array_unshift($keywords, $searchKeyword);

            if (count($keywords) > 5) {
                array_pop($keywords);
            }

            Cookie::queue('search_keywords', json_encode($keywords), 60 * 24 * 30);
            $this->keywords = $keywords;
        }
    }

    public function getQueryArray($historyKeyword = null)
    {
        return [
            'kw' => $historyKeyword ?? $this->searchKeyword,
            'p' => $this->p,
            'c' => $this->c,
            'yv' => $this->yv,
            'di' => $this->di,
            'sn' => $this->sn,
            'g' => $this->g,
            'locale' => $this->lang,
        ];
    }

    public function setSearchKeyword(string $historyKeyword)
    {
        if ($historyKeyword === '') {
            return;
        }

        $this->saveCookie($historyKeyword);
        return redirect()->route('search-gazette-result', $this->getQueryArray($historyKeyword));
    }

    public function submitSearchKeyword()
    {
        if ($this->searchKeyword !== '') {
            $this->saveCookie($this->searchKeyword);
        }
        return redirect()->route('search-gazette-result', $this->getQueryArray());
    }

    public function updated($key, $value)
    {
        if ($key === 'searchKeyword') {
            $this->dispatch('change-keyword', keyword: $value);
        }
    }

    public function render()
    {
        return view('livewire.search.search-gazette-bar');
    }
}
