<div>
    <!--        banner         -->
    <section class="gld-banner-container" id="homepage-banner" aria-labelledby="homepage-banner-heading" style="background: url('{{ asset('images/homepage/home-banner.png') }}') no-repeat center;">
        <div class="gld-banner-left">
            <div class="gld-banner-slogan-box">
                <h1 class="banner-fs gld-highlight-title" id="homepage-banner-heading">
                    {{ __('general.home.banner_left') }}
                </h1>
{{--                <form class="gld-searchbar" wire:submit="redirectToSearchResult">--}}
                    <!-- <a href="{{ route('search-gazette', app()->getLocale()) }}" class="gld-searchbar label1" type="submit" title="{{ __('general.home.banner_search_button') }}">
                        {{ __('general.home.banner_search') }}
                        <span class="gld-svg-container">
                            <span class="search-icon svg"></span>
                        </span>
                    </a> -->
{{--                    <label aria-label="Search Gazette Notice">--}}
{{--                        <input type="text" placeholder="{{ __('general.home.banner_search') }}" class="label1" wire:model="setSearchKeyword"/>--}}
{{--                        <button type="submit" title="{{ __('general.home.banner_search_button') }}">--}}
{{--                          <span class="gld-svg-container">--}}
{{--                            <span class="search-icon svg"></span>--}}
{{--                          </span>--}}
{{--                        </button>--}}
{{--                    </label>--}}
{{--                </form>--}}
            </div>
        </div>
    </section>
    <!--        gazette-notice         -->
    <section class="gld-main-wrapper" id="homepage-gazette-notice">
        <div class="gld-main-container">
            <div class="gld-section-heading">
                <h2 class="h2 gld-highlight-title">{{ __('general.home.gazette_notice_title') }}</h2>
            </div>
            <div class="gld-section-content">
                <!--        search-all-gazette         -->
                <a class="category-box" id="search-all-gazette" href="{{ route('search-gazette', ['locale' => app()->getLocale()]) }}">
                    <div class="category-icon">
                        <div class="gld-img-container">
                            <img src="{{ asset('images/homepage/svg/search-all.svg') }}" alt="{{ __('general.home.search_all_gazette_title') }}"/>
                        </div>
                    </div>
                    <div class="category-text">
                        <div class="subtitle1 category-title">{{ __('general.home.search_all_gazette_title') }}</div>
                        <div class="body1 category-desc">
                            {{ __('general.home.search_all_gazette_desc') }}
                        </div>
                    </div>
                    <div class="category-arrow-button">
                        <div class="gld-svg-container">
                            <span class="arrow-right-icon svg"></span>
                        </div>
                    </div>
                </a>
                <!--        search-list-of-gazette-gazette         -->
                <a class="category-box" id="search-list-of-gazette-gazette" href="{{ route('list-of-gazette', ['locale' => app()->getLocale()]) }}">
                    <div class="category-icon">
                        <div class="gld-img-container">
                            <img src="{{ asset('images/homepage/svg/gazette-list.svg') }}" alt="List of Gazette"/>
                        </div>
                    </div>
                    <div class="category-text">
                        <div class="subtitle1 category-title gazette-notice-latest" data-content="{{ __('general.home.latest') }}">
                            {{ __('general.home.list_of_gazette_title') }}
                        </div>
                        <div class="body1 category-desc">
                            {{ __('general.home.list_of_gazette_desc') }}
                        </div>
                    </div>
                    <div class="category-arrow-button">
                        <div class="gld-svg-container">
                            <span class="arrow-right-icon svg"></span>
                        </div>
                    </div>
                </a>
                <div class="homepage-notice-supplement-container" id="supplement">
                    <!--        gov-notice         -->
                    <a class="category-box" id="gov-notice" href="{{ route('search-gazette-result', ['locale' => app()->getLocale(), 'c' => 1]) }}#search-gazette-result-table">
                        <div class="category-icon">
                            <div class="gld-img-container">
                                <img src="{{ asset('images/homepage/svg/gov-notice.svg') }}" alt="{{ __('general.home.government_notice_title') }}"/>
                            </div>
                        </div>
                        <div class="category-text">
                            <div class="subtitle1 category-title">{{ __('general.home.government_notice_title') }}</div>
                            <div class="body1 category-desc">
                                {{ __('general.home.government_notice_desc') }}
                            </div>
                        </div>
                        <div class="category-arrow-button">
                            <div class="gld-svg-container">
                                <span class="arrow-right-icon svg"></span>
                            </div>
                        </div>
                    </a>
                    <!--        legal-notice         -->
                    <a class="category-box" id="legal-notice" href="{{ route('search-gazette-result', ['locale' => app()->getLocale(), 'c' => 2]) }}#search-gazette-result-table">
                        <div class="category-icon">
                            <div class="gld-img-container">
                                <img src="{{ asset('images/homepage/svg/legal-notice.svg') }}" alt="{{ __('general.home.legal_notice_title') }}"/>
                            </div>
                        </div>
                        <div class="category-text">
                            <div class="subtitle1 category-title">{{ __('general.home.legal_notice_title') }}</div>
                            <div class="body1 category-desc">
                                {!! __('general.home.legal_notice_desc') !!}
                            </div>
                        </div>
                        <div class="category-arrow-button">
                            <div class="gld-svg-container">
                                <span class="arrow-right-icon svg"></span>
                            </div>
                        </div>
                    </a>
                    <!--        supplement-no-4         -->
                    <a class="category-box" id="supplement-no-4" href="{{ route('search-gazette-result', ['locale' => app()->getLocale(), 'c' => 3]) }}#search-gazette-result-table">
                        <div class="category-icon">
                            <div class="gld-img-container">
                                <img src="{{ asset('images/homepage/svg/supplement-04.svg') }}" alt="{{ __('general.home.supplement_no_4_title') }}"/>
                            </div>
                        </div>
                        <div class="category-text">
                            <div class="subtitle1 category-title">{{ __('general.home.supplement_no_4_title') }}</div>
                            <div class="body1 category-desc">
                                {{ __('general.home.supplement_no_4_desc') }}
                            </div>
                        </div>
                        <div class="category-arrow-button">
                            <div class="gld-svg-container">
                                <span class="arrow-right-icon svg"></span>
                            </div>
                        </div>
                    </a>
                    <!--        supplement-no-6         -->
                    <a class="category-box" id="supplement-no-6" href="{{ route('search-gazette-result', ['locale' => app()->getLocale(), 'c' => 4]) }}#search-gazette-result-table">
                        <div class="category-icon">
                            <div class="gld-img-container">
                                <img src="{{ asset('images/homepage/svg/supplement-06.svg') }}" alt="{{ __('general.home.supplement_no_6_title') }}"/>
                            </div>
                        </div>
                        <div class="category-text">
                            <div class="subtitle1 category-title">{{ __('general.home.supplement_no_6_title') }}</div>
                            <div class="body1 category-desc">{{ __('general.home.supplement_no_6_desc') }}</div>
                        </div>
                        <div class="category-arrow-button">
                            <div class="gld-svg-container">
                                <span class="arrow-right-icon svg"></span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <p class="subtitle2">
                {{ __('general.home.banner_right') }}
            </p>
        </div>
    </section>
    <!--        contact-us         -->
    <section class="gld-main-wrapper" id="contact-us" style="background-image: url('{{ asset('images/homepage/contact-us-bg.png') }}');">
        <div class="gld-main-container">
            <div class="contact-us-left">
                <a href="{{ route('contact-us', ['locale' => app()->getLocale()]) }}" class="contact-us-link" >
                    <h2 class="gld-highlight-title h2">{{ __('general.contact_us.contact_us') }}</h2>
                    <div class="circle-arrow-right-button">
                        <div class="gld-svg-container">
                            <span class="arrow-right-icon svg"></span>
                        </div>
                    </div>
                </a>
            </div>
            <!--        contact-us-address         -->
            <div class="contact-us-right">
                <div id="homepage-address">
                    <div class="subtitle2 contact-us-list-title">{{ __('general.contact_us.address') }}</div>
                    <ul class="contact-us-list">
                        <li class="subtitle2 contact-us-list-item">
                            <a href="{!! $data['general_address'] !!}" target="_blank">
                                <livewire:contact-us.icons type="address" />
                                <span class="label1">{!! $data['general_address'] !!}</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <!--        contact-us-general-enquiries         -->
                <div id="homepage-general-enquiries">
                    <div class="subtitle2 contact-us-list-title">
                        {{ __('general.contact_us.general_enquiry') }}
                    </div>
                    <ul class="contact-us-list">
                        <li class="subtitle2 contact-us-list-item">
                            <a href="mailto:{!! $data['general_email'] !!}">
                                <livewire:contact-us.icons type="email" />
                                <span class="label1">{!! $data['general_email'] !!}</span>
                            </a>
                        </li>
                        <li class="subtitle2 contact-us-list-item">
                            <a href="tel:+{!! $data['general_fax'] !!}">
                                <livewire:contact-us.icons type="fax" />
                                <span class="label1">{!! $data['general_fax'] !!}</span>
                            </a>
                        </li>
                        <li class="subtitle2 contact-us-list-item">
                            <div class="label1 contact-us-list-subtitle">
                                {{ __('general.contact_us.gazette_team') }}
                            </div>
                            <a href="tel:+{!! $data['general_gazette_tel'] !!}">
                                <livewire:contact-us.icons type="tel" />
                                <span class="label1">{!! $data['general_gazette_tel'] !!}</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
    <?php
    $locale = app()->getLocale();
    ?>
    @if (config('app.pnsps_service_announcement'))
        <div id="announcement-popup" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.4); z-index:9999; justify-content:center; align-items:center;">
            <div style="background:#fff; padding:2rem; border-radius:8px; max-width:80%; box-shadow:0 2px 16px rgba(0,0,0,0.2); position:relative; overflow-y:auto;">
                <button id="close-announcement-popup" style="position:absolute; top:8px; right:12px; background:none; border:none; font-size:1.5rem; cursor:pointer;">&times;</button>
                <p class="gld-highlight-title h3">{!! $data['announcement_title'] !!}</p>
                <br>
                <u class="subtitle2">{!! $data['announcement_subtitle'] !!}</u>
                {!! $data['announcement_content'] !!}
                <br><br>
                <u class="subtitle2">{!! $data['announcement_subtitle2'] !!}</u>
                <div style="width: 100%; margin: auto; display: flex; gap: var(--spacing-xxx-big);">
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-big);">
                        <div class="subtitle2">{!! $data['announcement_card1_title'] !!}</div>
                        <div>
                            <ul style="display: flex; flex-direction: column; gap: var(--spacing-x-sm);">
                                <li>
                                    <span class="label2">{!! $data['announcement_card1_name'] !!}</span>
                                </li>
                                <li>
                                    <span class="label2">{!! $data['announcement_card1_address'] !!}</span>
                                </li>
                                <li>
                                    <span class="label2">{!! $data['announcement_card1_tel'] !!}</span>
                                </li>
                                <li>
                                    <span class="label2">{!! $data['announcement_card1_email'] !!}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-big);">
                        <div class="subtitle2">{!! $data['announcement_card2_title'] !!}</div>
                        <div>
                            <ul style="display: flex; flex-direction: column; gap: var(--spacing-x-sm);">
                                <li>
                                    <span class="label2">{!! $data['announcement_card2_name'] !!}</span>
                                </li>
                                <li>
                                    <span class="label2">{!! $data['announcement_card2_address'] !!}</span>
                                </li>
                                <li>
                                    <span class="label2">{!! $data['announcement_card2_tel'] !!}</span>
                                </li>
                                <li>
                                    <span class="label2">{!! $data['announcement_card2_email'] !!}</a></span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script>
            window.addEventListener('DOMContentLoaded', function() {
                var popup = document.getElementById('announcement-popup');
                if (popup) {
                    popup.style.display = 'flex';
                    document.getElementById('close-announcement-popup').onclick = function() {
                        popup.style.display = 'none';
                    };
                }
            });
        </script>
    @endif
</div>
